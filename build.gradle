plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.8'
    id 'io.spring.dependency-management' version '1.1.6'
    id "jacoco"
    id 'org.sonarqube' version '4.4.1.3373'
}

jacoco {
    toolVersion = "0.8.10"
}

group = 'com.concirrus'
version = '0.0.1-SNAPSHOT'

jar {
    archivesBaseName = "reporting-service"
    project.version = ""
}

java {
    sourceCompatibility = '17'
}

ext {
    set('springCloudGcpVersion', "5.4.3")
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    implementation 'javax.persistence:javax.persistence-api:2.2'
    compileOnly 'org.projectlombok:lombok:1.18.20'
    annotationProcessor 'org.projectlombok:lombok:1.18.20'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-validation'

    // mongo
    implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'

    // Apache POI
    implementation 'org.apache.poi:poi:5.2.3'
    implementation 'org.apache.poi:poi-ooxml:5.2.3'

    // kubernetes
    implementation 'org.springframework.cloud:spring-cloud-starter-kubernetes-client-config:2.1.9'

    // Storage
    implementation 'software.amazon.awssdk:s3:2.20.109'
    implementation 'com.google.cloud:google-cloud-storage'

    // GCP Pub/Sub
    implementation 'com.google.cloud:spring-cloud-gcp-starter-pubsub'
    implementation 'org.springframework.integration:spring-integration-core'

    // https://mvnrepository.com/artifact/javax.xml.bind/jaxb-api
    implementation group: 'javax.xml.bind', name: 'jaxb-api', version: '2.4.0-b180830.0359'

    implementation 'jakarta.validation:jakarta.validation-api:3.0.0'

    // https://mvnrepository.com/artifact/org.apache.commons/commons-text
    implementation 'org.apache.commons:commons-text:1.11.0'

    // https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-aop
    implementation 'org.springframework.boot:spring-boot-starter-aop'

}
dependencyManagement {
    imports {
        mavenBom "com.google.cloud:spring-cloud-gcp-dependencies:${springCloudGcpVersion}"
    }
}
sonarqube {
    properties {
        property 'sonar.coverage.exclusions', "**/com/concirrus/reporting/annotation/**," +
                "**/com/concirrus/reporting/config/**," +
                "**/com/concirrus/reporting/constant/**," +
                "**/com/concirrus/reporting/dto/**," +
                "**/com/concirrus/reporting/entity/**," +
                "**/com/concirrus/reporting/enums/**," +
                "**/com/concirrus/reporting/event/**," +
                "**/com/concirrus/reporting/handler/**," +
                "**/com/concirrus/reporting/listener/**," +
                "**/com/concirrus/reporting/model/**," +
                "**/com/concirrus/reporting/dal/**," +
                "**/com/concirrus/reporting/ReportingServiceApplication.java"
    }
}

tasks.named('test') {
    useJUnitPlatform()
}


jacocoTestReport {
    reports {
        html.required = true
        xml.required = true
        xml.destination file("${buildDir}/reports/jacoco.xml")
    }
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
}


