# Getting Started

* This is a reporting microservice which will be used as a blueprint to create new microservices in the future.
* The reporting of this service will be followed entirely, so that the structure remains same throughout in new applications as well.

## Prerequisites

* Java 17
* Gradle
* Make sure gnu-sed is installed on your system
* If not run ```brew install gnu-sed``` on  your terminal.

## How to rename project
* In the root path of your project run this command
* ```./rename-service.sh YOUR_NEW_NAME```
* e.g. ```./rename-service.sh policy```

## config

* AWS SQS and AWS S3 configurations are already set up in the 'AWSConfig' class under the 'config' package.
* Postgres is also being set up.
* Make sure to add database first in your postgresql client/ database administration tool.
* To use these configurations provide the necessary arguments in the application.properties file and application-local.properties file.
* A default entity/model/table has also been set up under the 'entity' package named 'Reporting' to provide the default structure for entities.
* Feel free to remove or modify it.

## constant

* Create a Config class in this package to declare static string messages.

## dto

* Create DTOs to receive and send data in and out of service.
* Create an AppResponse DTO to always send response in the same format to the application layer.

## controller

* Return type of every method in the Controller class must be ResponseEntity<AppResponseDTO>.
* Set HttpStatus and body of the ResponseEntity class before sending response.

## entity and repository

* Use Postgres for database.
* Use Hibernate as your ORM.
* Use Lombok Library for getters and setters.

## service

* Use @Autowired annotation for dependency injections.
* Use @Value annotation for setting the value of a field.

## other

* Use slf4j Logger for logging messages.
* Use application.properties file for setting up properties in your application.
* Use application-local.properties file for local testing purposes.
