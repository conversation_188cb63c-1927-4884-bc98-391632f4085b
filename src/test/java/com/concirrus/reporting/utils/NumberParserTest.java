package com.concirrus.reporting.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

class NumberParserTest {
    /**
     * Method under test: {@link NumberParser#parseToDouble(String)}
     */
    @Test
    void testParseToDouble() {
        // Arrange, Act and Assert
        assertEquals(42.00, NumberParser.parseToDouble("42"));
        assertEquals(42.00, NumberParser.parseToDouble("42."));
        assertEquals(41.56, NumberParser.parseToDouble("41.56"));
        assertEquals(41.70, NumberParser.parseToDouble("41.7"));
        assertEquals(41.89, NumberParser.parseToDouble("41.89"));
        assertEquals(41.36, NumberParser.parseToDouble("41.364"));
        assertEquals(41.37, NumberParser.parseToDouble("41.365"));
        assertEquals(41.37, NumberParser.parseToDouble("41.366"));
    }

    @Test
    void testEmptyString() {
        assertThrows(NumberFormatException.class, () -> NumberParser.parseToDouble(""));
    }

    @Test
    void testStringWithSpaces() {
        assertThrows(NumberFormatException.class, () -> NumberParser.parseToDouble("   "));
    }

    @Test
    void testStringWithNonNumericCharacters() {
        assertThrows(NumberFormatException.class, () -> NumberParser.parseToDouble("abc"));
    }

    @Test
    void testStringWithInvalidIntegerFormat() {
        assertThrows(NumberFormatException.class, () -> NumberParser.parseToDouble("12.34.56"));
    }

    @Test
    void testStringWithDecimalNumber() {
        assertEquals(123.45, NumberParser.parseToDouble("123.45"));
    }

    @Test
    void testStringWithComa() {
        assertEquals(12345, NumberParser.parseToDouble("123,45"));
    }

    @Test
    void testStringWithIntegerNumber() {
        assertEquals(678.00, NumberParser.parseToDouble("678"));
    }

    @Test
    void testStringWithNegativeDecimalNumber() {
        assertEquals(-123.45, NumberParser.parseToDouble("-123.45"));
    }

    @Test
    void testStringWithNegativeIntegerNumber() {
        assertEquals(-678.00, NumberParser.parseToDouble("-678"));
    }
}
