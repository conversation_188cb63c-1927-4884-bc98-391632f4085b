package com.concirrus.reporting.utils;

import com.concirrus.reporting.model.report.ASUAgingReportModel;
import com.concirrus.reporting.model.report.BordereauReportModel;
import org.bson.Document;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class UtilityMapperTest {
    @Test
    void testBordereauReportWithEmptyDocuments() {
        // Arrange
        List<Document> documents = new ArrayList<>();
        Map<String, Map<String, String>> officesMap = new HashMap<>();
        Map<String, List<Document>> endorsementsMap = new HashMap<>();
        Map<String, Document> policyIdToBoundPolicyDocumentMap = new HashMap<>();

        // Act
        List<BordereauReportModel> result = UtilityMapper.buildBordereauReportList(documents, officesMap, endorsementsMap, policyIdToBoundPolicyDocumentMap);

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    void testASUAgingReportWithEmptyDocuments() {
        // Arrange
        List<Document> documents = new ArrayList<>();
        Map<String, Map<String, String>> invoicesMap = new HashMap<>();

        // Act
        List<ASUAgingReportModel> result = UtilityMapper.buildASUAgingReportList(documents, invoicesMap);

        // Assert
        assertEquals(0, result.size());
    }
}
