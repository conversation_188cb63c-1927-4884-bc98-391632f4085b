package com.concirrus.reporting.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.mongodb.core.query.Query;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class QueryUtilsTest {
    @Test
    void testPaginationQuery() {
        // Arrange
        Query query = new Query();
        PageRequest req = PageRequest.of(1, 10); // page number = 1, page size = 10

        // Act
        Query result = QueryUtils.getPaginationQueryForMongo(query, req);

        // Assert
        assertEquals(10, result.getLimit());
        assertEquals(10, result.getSkip());
    }

    @Test
    void testNullQuery() {
        // Arrange
        Query query = null;
        PageRequest req = PageRequest.of(0, 20); // page number = 0, page size = 20

        // Act
        Query result = QueryUtils.getPaginationQueryForMongo(query, req);

        // Assert
        assertEquals(20, result.getLimit());
        assertEquals(0, result.getSkip());
    }

    @Test
    void testQueryWithDifferentPageRequest() {
        // Arrange
        Query query = new Query();
        PageRequest req = PageRequest.of(3, 5); // page number = 3, page size = 5

        // Act
        Query result = QueryUtils.getPaginationQueryForMongo(query, req);

        // Assert
        assertEquals(5, result.getLimit());
        assertEquals(15, result.getSkip());
    }
}
