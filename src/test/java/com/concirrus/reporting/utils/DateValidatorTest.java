package com.concirrus.reporting.utils;


import com.concirrus.reporting.model.exception.BadRequestException;
import com.concirrus.reporting.model.exception.InvalidArgumentsException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
class DateValidatorTest {

    @Test
    void testNullDates() {
        assertThrows(BadRequestException.class, () -> DateValidator.validateDate(null, null));
    }

    @Test
    void testNullStartDate() {
        LocalDate endDate = LocalDate.now();

        assertThrows(BadRequestException.class, () -> DateValidator.validateDate(null, endDate));
    }

    @Test
    void testNullEndDate() {
        LocalDate startDate = LocalDate.now();
        assertThrows(BadRequestException.class, () -> DateValidator.validateDate(startDate, null));
    }

    @Test
    void testStartDateAfterEndDate() {
        LocalDate startDate = LocalDate.of(2024, 2, 29);
        LocalDate endDate = LocalDate.of(2024, 2, 28);
        assertThrows(InvalidArgumentsException.class, () -> {
            DateValidator.validateDate(startDate, endDate);
        });
    }

    @Test
    void testValidDates() {
        LocalDate startDate = LocalDate.of(2024, 2, 28);
        LocalDate endDate = LocalDate.of(2024, 2, 29);
        DateValidator.validateDate(startDate, endDate);
    }
}
