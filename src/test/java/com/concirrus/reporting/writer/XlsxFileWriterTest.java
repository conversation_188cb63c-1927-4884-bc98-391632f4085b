package com.concirrus.reporting.writer;

import com.concirrus.reporting.model.report.BordereauReportModel;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
class XlsxFileWriterTest {

    @InjectMocks
    private XlsxFileWriter xlsxWriter;

    @Test
    void testGenerateExcel_ValidInput() {
        List<Object> testData = Collections.singletonList(new BordereauReportModel());
        byte[] result = xlsxWriter.generateExcel(testData);
        assertNotNull(result);
        // Add further assertions based on your expectations
    }


    @Test
    void testGenerateExcel_NullInput() {
        byte[] result = xlsxWriter.generateExcel(null);
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    void testGenerateExcel_UnsupportedDataType() {
        // Prepare unsupported data type
        List<Integer> testData = Collections.singletonList(123);
        byte[] result = xlsxWriter.generateExcel(testData);
        assertNotNull(result);
        assertEquals(0, result.length);
    }
}
