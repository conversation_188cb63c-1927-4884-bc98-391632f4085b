package com.concirrus.reporting.controller;

import com.concirrus.reporting.dal.AuditDAL;
import com.concirrus.reporting.dal.EndorsementDAL;
import com.concirrus.reporting.dal.PolicyDAL;
import com.concirrus.reporting.dal.impl.ReportingDALImpl;
import com.concirrus.reporting.dto.PagedResponse;
import com.concirrus.reporting.dto.ReportRequestDTO;
import com.concirrus.reporting.dto.ReportRequestInfoDTO;
import com.concirrus.reporting.entity.ReportRequestInfo;
import com.concirrus.reporting.enums.ReportingType;
import com.concirrus.reporting.helper.StorageService;
import com.concirrus.reporting.sal.AuditSAL;
import com.concirrus.reporting.sal.BrokerageHubSAL;
import com.concirrus.reporting.sal.InvoiceSAL;
import com.concirrus.reporting.sal.UserSAL;
import com.concirrus.reporting.service.ReportingService;
import com.concirrus.reporting.service.impl.ReportingServiceImpl;
import com.concirrus.reporting.writer.XlsxWriter;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.MutablePair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static com.concirrus.reporting.constant.DBConstant.CREATED_AT;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReportingControllerTest {
    @Autowired
    private ReportingController reportingController;

    @MockBean
    private ReportingService reportingService;

    @MockBean
    private StorageService storageService;

    /**
     * Method under test:
     * {@link ReportingController#createReportRequest(ReportRequestDTO, String, String)}
     */
    @Test
    void testCreateReportRequest() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        ReportRequestInfo reportRequestInfo = new ReportRequestInfo();
        reportRequestInfo.setClientId("42");
        reportRequestInfo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo.setErrorMessage("An error occurred");
        reportRequestInfo.setFileDownloadLink("File Download Link");
        reportRequestInfo.setFileName("foo.txt");
        reportRequestInfo.setId("42");
        reportRequestInfo.setStatus("Status");

        ReportRequestInfo reportRequestInfo2 = new ReportRequestInfo();
        reportRequestInfo2.setClientId("42");
        reportRequestInfo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo2.setErrorMessage("An error occurred");
        reportRequestInfo2.setFileDownloadLink("File Download Link");
        reportRequestInfo2.setFileName("foo.txt");
        reportRequestInfo2.setId("42");
        reportRequestInfo2.setStatus("Status");
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.updateReportRequest(Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<ReportRequestInfo>any())).thenReturn(reportRequestInfo2);
        when(reportingDAL.addReportRequest(Mockito.<ReportRequestInfo>any())).thenReturn(reportRequestInfo);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);
        doNothing().when(eventPublisher).publishEvent(Mockito.<ApplicationEvent>any());
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        AuditDAL auditDAL = mock(AuditDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        UserSAL userSAL = mock(UserSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
//        ReportingController reportingController = new ReportingController(
//                new ReportingServiceImpl(policyDAL, reportingDAL, endorsementDAL, invoiceSAL, brokerageHubSAL, auditSAL, xlsxWriter,
//                        eventPublisher, new S3HelperImpl(new CloudConfig()), "s3://bucket-name/object-key", 1, 8080));

        ReportingController reportingController = new ReportingController(
                new ReportingServiceImpl(policyDAL, reportingDAL, endorsementDAL, auditDAL, invoiceSAL, brokerageHubSAL, auditSAL, userSAL, xlsxWriter,
                        eventPublisher, storageService, "s3://bucket-name/object-key", 1, 8080));

        ReportRequestDTO reportRequestDTO = new ReportRequestDTO();
        reportRequestDTO.setEndDate(LocalDate.of(1970, 1, 1));
        reportRequestDTO.setReportingType(ReportingType.BORDEREAU);
        reportRequestDTO.setStartDate(LocalDate.of(1970, 1, 1));

        // Act
        PagedResponse actualCreateReportRequestResult = reportingController.createReportRequest(reportRequestDTO, "42",
                "42");

        // Assert
        verify(reportingDAL).addReportRequest(isA(ReportRequestInfo.class));
        verify(reportingDAL).updateReportRequest(eq("42"), eq("42"), isA(ReportRequestInfo.class));
        verify(eventPublisher).publishEvent(isA(ApplicationEvent.class));
        assertEquals("Request Processed Successfully", actualCreateReportRequestResult.getMessage());
        assertEquals("Request submitted successfully with id : 42", actualCreateReportRequestResult.getData());
        assertTrue(actualCreateReportRequestResult.isSuccess());
    }

    /**
     * Method under test:
     * {@link ReportingController#createReportRequest(ReportRequestDTO, String, String)}
     */
    @Test
    void testCreateReportRequest2() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        ReportingServiceImpl reportingService = mock(ReportingServiceImpl.class);
        when(reportingService.createReportRequest(Mockito.<ReportRequestDTO>any(), Mockito.<String>any(),
                Mockito.<String>any())).thenReturn("Create Report Request");
        ReportingController reportingController = new ReportingController(reportingService);

        ReportRequestDTO reportRequestDTO = new ReportRequestDTO();
        reportRequestDTO.setEndDate(LocalDate.of(1970, 1, 1));
        reportRequestDTO.setReportingType(ReportingType.BORDEREAU);
        reportRequestDTO.setStartDate(LocalDate.of(1970, 1, 1));

        // Act
        PagedResponse actualCreateReportRequestResult = reportingController.createReportRequest(reportRequestDTO, "42",
                "42");

        // Assert
        verify(reportingService).createReportRequest(isA(ReportRequestDTO.class), eq("42"), eq("42"));
        assertEquals("Request Processed Successfully", actualCreateReportRequestResult.getMessage());
        assertEquals("Request submitted successfully with id : Create Report Request",
                actualCreateReportRequestResult.getData());
        assertTrue(actualCreateReportRequestResult.isSuccess());
    }

    /**
     * Method under test:
     * {@link ReportingController#createReportRequest(ReportRequestDTO, String, String)}
     */
    @Test
    void testCreateReportRequest3() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        ReportingServiceImpl reportingService = mock(ReportingServiceImpl.class);
        when(reportingService.createReportRequest(Mockito.<ReportRequestDTO>any(), Mockito.<String>any(),
                Mockito.<String>any())).thenReturn("Create Report Request");
        ReportingController reportingController = new ReportingController(reportingService);
        ReportRequestDTO reportRequestDTO = mock(ReportRequestDTO.class);
        when(reportRequestDTO.getReportingType()).thenReturn(ReportingType.ASU_AGING);
        doNothing().when(reportRequestDTO).setEndDate(Mockito.<LocalDate>any());
        doNothing().when(reportRequestDTO).setReportingType(Mockito.<ReportingType>any());
        doNothing().when(reportRequestDTO).setStartDate(Mockito.<LocalDate>any());
        reportRequestDTO.setEndDate(LocalDate.of(1970, 1, 1));
        reportRequestDTO.setReportingType(ReportingType.BORDEREAU);
        reportRequestDTO.setStartDate(LocalDate.of(1970, 1, 1));

        // Act
        PagedResponse actualCreateReportRequestResult = reportingController.createReportRequest(reportRequestDTO, "42",
                "42");

        // Assert
        verify(reportRequestDTO).setEndDate(isA(LocalDate.class));
        verify(reportRequestDTO).setReportingType(ReportingType.BORDEREAU);
        verify(reportRequestDTO).setStartDate(isA(LocalDate.class));
        verify(reportingService).createReportRequest(isA(ReportRequestDTO.class), eq("42"), eq("42"));
        assertEquals("Request Processed Successfully", actualCreateReportRequestResult.getMessage());
        assertEquals("Request submitted successfully with id : Create Report Request",
                actualCreateReportRequestResult.getData());
        assertTrue(actualCreateReportRequestResult.isSuccess());
    }

    /**
     * Method under test:
     * {@link ReportingController#getReportRequestInfoList(int, int, org.springframework.data.domain.Sort.Direction, String, String)}
     */
    @Test
    void testGetReportRequestInfoList() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.getPairOfTotalCountAndReportRequestInfoList(Mockito.<PageRequest>any(), Mockito.<String>any(),
                Mockito.<LocalDate>any())).thenReturn(new ImmutablePair<>(3L, new ArrayList<>()));
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        AuditDAL auditDAL = mock(AuditDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        UserSAL userSAL = mock(UserSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);

        // Act
        PagedResponse actualReportRequestInfoList = (new ReportingController(
                new ReportingServiceImpl(policyDAL, reportingDAL, endorsementDAL, auditDAL, invoiceSAL, brokerageHubSAL, auditSAL, userSAL, xlsxWriter,
                        eventPublisher, storageService, "s3://bucket-name/object-key", 1, 8080)))
                .getReportRequestInfoList(10, 3, Sort.Direction.ASC, CREATED_AT, "42");

        // Assert
        verify(reportingDAL).getPairOfTotalCountAndReportRequestInfoList(isA(PageRequest.class), eq("42"),
                isA(LocalDate.class));
        assertEquals("Request Processed Successfully", actualReportRequestInfoList.getMessage());
        assertEquals(3L, actualReportRequestInfoList.getTotalItems().longValue());
        assertTrue(actualReportRequestInfoList.isSuccess());
        assertTrue(((List<Object>) actualReportRequestInfoList.getData()).isEmpty());
    }

    /**
     * Method under test:
     * {@link ReportingController#getReportRequestInfoList(int, int, org.springframework.data.domain.Sort.Direction, String, String)}
     */
    @Test
    void testGetReportRequestInfoList2() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        ReportingService reportingService = mock(ReportingService.class);
        ImmutablePair<Long, List<ReportRequestInfoDTO>> nullPairResult = ImmutablePair.nullPair();
        when(
                reportingService.getPairOfTotalCountAndReportRequestInfoList(Mockito.<PageRequest>any(), Mockito.<String>any()))
                .thenReturn(nullPairResult);

        // Act
        PagedResponse actualReportRequestInfoList = (new ReportingController(reportingService)).getReportRequestInfoList(10,
                3, Sort.Direction.ASC, CREATED_AT,"42");

        // Assert
        verify(reportingService).getPairOfTotalCountAndReportRequestInfoList(isA(PageRequest.class), eq("42"));
        assertEquals("Request Processed Successfully", actualReportRequestInfoList.getMessage());
        assertNull(actualReportRequestInfoList.getTotalItems());
        assertNull(actualReportRequestInfoList.getData());
        assertTrue(actualReportRequestInfoList.isSuccess());
    }

    /**
     * Method under test:
     * {@link ReportingController#getReportRequestInfoList(int, int, org.springframework.data.domain.Sort.Direction, String, String)}
     */
    @Test
    void testGetReportRequestInfoList3() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        ReportingService reportingService = mock(ReportingService.class);
        when(
                reportingService.getPairOfTotalCountAndReportRequestInfoList(Mockito.<PageRequest>any(), Mockito.<String>any()))
                .thenReturn(new MutablePair<>());

        // Act
        PagedResponse actualReportRequestInfoList = (new ReportingController(reportingService)).getReportRequestInfoList(10,
                3, Sort.Direction.ASC, CREATED_AT,"42");

        // Assert
        verify(reportingService).getPairOfTotalCountAndReportRequestInfoList(isA(PageRequest.class), eq("42"));
        assertEquals("Request Processed Successfully", actualReportRequestInfoList.getMessage());
        assertNull(actualReportRequestInfoList.getTotalItems());
        assertNull(actualReportRequestInfoList.getData());
        assertTrue(actualReportRequestInfoList.isSuccess());
    }
}
