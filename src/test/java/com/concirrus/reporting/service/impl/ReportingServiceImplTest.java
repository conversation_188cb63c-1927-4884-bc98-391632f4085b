package com.concirrus.reporting.service.impl;

import com.concirrus.reporting.dal.AuditDAL;
import com.concirrus.reporting.dal.EndorsementDAL;
import com.concirrus.reporting.dal.PolicyDAL;
import com.concirrus.reporting.dal.ReportingDAL;
import com.concirrus.reporting.dal.impl.ReportingDALImpl;
import com.concirrus.reporting.dto.ReportRequestDTO;
import com.concirrus.reporting.dto.ReportRequestInfoDTO;
import com.concirrus.reporting.entity.ReportRequestInfo;
import com.concirrus.reporting.enums.ReportingType;
import com.concirrus.reporting.helper.StorageService;
import com.concirrus.reporting.model.exception.TooEarlyRequestException;
import com.concirrus.reporting.sal.AuditSAL;
import com.concirrus.reporting.sal.BrokerageHubSAL;
import com.concirrus.reporting.sal.InvoiceSAL;
import com.concirrus.reporting.sal.UserSAL;
import com.concirrus.reporting.writer.XlsxWriter;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.PageRequest;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReportingServiceImplTest {
    @MockBean
    private ApplicationEventPublisher applicationEventPublisher;

    @MockBean
    private BrokerageHubSAL brokerageHubSAL;

    @MockBean
    private EndorsementDAL endorsementDAL;

    @MockBean
    private InvoiceSAL invoiceSAL;

    @MockBean
    private PolicyDAL policyDAL;

    @MockBean
    private AuditDAL auditDAL;

    @MockBean
    private UserSAL userSAL;

    @MockBean
    private ReportingDAL reportingDAL;

    @Autowired
    private ReportingServiceImpl reportingServiceImpl;

    @MockBean
    private StorageService storageService;

    @MockBean
    private XlsxWriter xlsxWriter;

    /**
     * Method under test:
     * {@link ReportingServiceImpl#createReportRequest(ReportRequestDTO, String, String)}
     */
    @Test
    void testCreateReportRequest() {


        // Arrange
        ReportRequestInfo reportRequestInfo = new ReportRequestInfo();
        reportRequestInfo.setClientId("42");
        reportRequestInfo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo.setErrorMessage("An error occurred");
        reportRequestInfo.setFileDownloadLink("File Download Link");
        reportRequestInfo.setFileName("foo.txt");
        reportRequestInfo.setId("42");
        reportRequestInfo.setStatus("Status");

        ReportRequestInfo reportRequestInfo2 = new ReportRequestInfo();
        reportRequestInfo2.setClientId("42");
        reportRequestInfo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo2.setErrorMessage("An error occurred");
        reportRequestInfo2.setFileDownloadLink("File Download Link");
        reportRequestInfo2.setFileName("foo.txt");
        reportRequestInfo2.setId("42");
        reportRequestInfo2.setStatus("Status");
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.updateReportRequest(Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<ReportRequestInfo>any())).thenReturn(reportRequestInfo2);
        when(reportingDAL.addReportRequest(Mockito.<ReportRequestInfo>any())).thenReturn(reportRequestInfo);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);
        doNothing().when(eventPublisher).publishEvent(Mockito.<ApplicationEvent>any());
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        AuditDAL auditDAL = mock(AuditDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        UserSAL userSAL = mock(UserSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
        ReportingServiceImpl reportingServiceImpl = new ReportingServiceImpl(policyDAL, reportingDAL, endorsementDAL,
                auditDAL, invoiceSAL, brokerageHubSAL, auditSAL, userSAL, xlsxWriter, eventPublisher, storageService,
                "s3://bucket-name/object-key", 1, 8080);

        ReportRequestDTO reportRequestDTO = new ReportRequestDTO();
        reportRequestDTO.setEndDate(LocalDate.of(1970, 1, 1));
        reportRequestDTO.setReportingType(ReportingType.BORDEREAU);
        reportRequestDTO.setStartDate(LocalDate.of(1970, 1, 1));

        // Act
        String actualCreateReportRequestResult = reportingServiceImpl.createReportRequest(reportRequestDTO, "42", "42");

        // Assert
        verify(reportingDAL).addReportRequest(isA(ReportRequestInfo.class));
        verify(reportingDAL).updateReportRequest(eq("42"), eq("42"), isA(ReportRequestInfo.class));
        verify(eventPublisher).publishEvent(isA(ApplicationEvent.class));
        assertEquals("42", actualCreateReportRequestResult);
    }

    /**
     * Method under test:
     * {@link ReportingServiceImpl#createReportRequest(ReportRequestDTO, String, String)}
     */
    @Test
    void testCreateReportRequest2() {


        // Arrange
        ReportRequestInfo reportRequestInfo = new ReportRequestInfo();
        reportRequestInfo.setClientId("42");
        reportRequestInfo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo.setErrorMessage("An error occurred");
        reportRequestInfo.setFileDownloadLink("File Download Link");
        reportRequestInfo.setFileName("foo.txt");
        reportRequestInfo.setId("42");
        reportRequestInfo.setStatus("Status");

        ReportRequestInfo reportRequestInfo2 = new ReportRequestInfo();
        reportRequestInfo2.setClientId("42");
        reportRequestInfo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo2.setErrorMessage("An error occurred");
        reportRequestInfo2.setFileDownloadLink("File Download Link");
        reportRequestInfo2.setFileName("foo.txt");
        reportRequestInfo2.setId("42");
        reportRequestInfo2.setStatus("Status");
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.updateReportRequest(Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<ReportRequestInfo>any())).thenReturn(reportRequestInfo2);
        when(reportingDAL.addReportRequest(Mockito.<ReportRequestInfo>any())).thenReturn(reportRequestInfo);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);
        doThrow(new TooEarlyRequestException("An error occurred")).when(eventPublisher)
                .publishEvent(Mockito.<ApplicationEvent>any());
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        AuditDAL auditDAL = mock(AuditDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        UserSAL userSAL = mock(UserSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
        ReportingServiceImpl reportingServiceImpl = new ReportingServiceImpl(policyDAL, reportingDAL, endorsementDAL,
                auditDAL, invoiceSAL, brokerageHubSAL, auditSAL, userSAL, xlsxWriter, eventPublisher, storageService,
                "s3://bucket-name/object-key", 1, 8080);

        ReportRequestDTO reportRequestDTO = new ReportRequestDTO();
        reportRequestDTO.setEndDate(LocalDate.of(1970, 1, 1));
        reportRequestDTO.setReportingType(ReportingType.BORDEREAU);
        reportRequestDTO.setStartDate(LocalDate.of(1970, 1, 1));

        // Act and Assert
        assertThrows(TooEarlyRequestException.class,
                () -> reportingServiceImpl.createReportRequest(reportRequestDTO, "42", "42"));
        verify(reportingDAL).addReportRequest(isA(ReportRequestInfo.class));
        verify(reportingDAL).updateReportRequest(eq("42"), eq("42"), isA(ReportRequestInfo.class));
        verify(eventPublisher).publishEvent(isA(ApplicationEvent.class));
    }

    /**
     * Method under test:
     * {@link ReportingServiceImpl#createReportRequest(ReportRequestDTO, String, String)}
     */
    @Test
    void testCreateReportRequest3() {


        // Arrange
        ReportRequestInfo reportRequestInfo = new ReportRequestInfo();
        reportRequestInfo.setClientId("42");
        reportRequestInfo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo.setErrorMessage("An error occurred");
        reportRequestInfo.setFileDownloadLink("File Download Link");
        reportRequestInfo.setFileName("foo.txt");
        reportRequestInfo.setId("42");
        reportRequestInfo.setStatus("Status");
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.addReportRequest(Mockito.<ReportRequestInfo>any())).thenReturn(reportRequestInfo);
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        AuditDAL auditDAL = mock(AuditDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        UserSAL userSAL = mock(UserSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);
        ReportingServiceImpl reportingServiceImpl = new ReportingServiceImpl(policyDAL, reportingDAL, endorsementDAL,
                auditDAL, invoiceSAL, brokerageHubSAL, auditSAL, userSAL, xlsxWriter, eventPublisher, storageService,
                "s3://bucket-name/object-key", 1, 8080);
        ReportRequestDTO reportRequestDTO = mock(ReportRequestDTO.class);
        when(reportRequestDTO.getStartDate()).thenThrow(new TooEarlyRequestException("An error occurred"));
        when(reportRequestDTO.getReportingType()).thenReturn(ReportingType.BORDEREAU);
        doNothing().when(reportRequestDTO).setEndDate(Mockito.<LocalDate>any());
        doNothing().when(reportRequestDTO).setReportingType(Mockito.<ReportingType>any());
        doNothing().when(reportRequestDTO).setStartDate(Mockito.<LocalDate>any());
        reportRequestDTO.setEndDate(LocalDate.of(1970, 1, 1));
        reportRequestDTO.setReportingType(ReportingType.BORDEREAU);
        reportRequestDTO.setStartDate(LocalDate.of(1970, 1, 1));

        // Act and Assert
        assertThrows(TooEarlyRequestException.class,
                () -> reportingServiceImpl.createReportRequest(reportRequestDTO, "42", "42"));
        verify(reportingDAL).addReportRequest(isA(ReportRequestInfo.class));
        verify(reportRequestDTO).getReportingType();
        verify(reportRequestDTO).getStartDate();
        verify(reportRequestDTO).setEndDate(isA(LocalDate.class));
        verify(reportRequestDTO).setReportingType(ReportingType.BORDEREAU);
        verify(reportRequestDTO).setStartDate(isA(LocalDate.class));
    }

    /**
     * Method under test:
     * {@link ReportingServiceImpl#createReportRequest(ReportRequestDTO, String, String)}
     */
    @Test
    void testCreateReportRequest4() {


        // Arrange
        ReportRequestInfo reportRequestInfo = new ReportRequestInfo();
        reportRequestInfo.setClientId("42");
        reportRequestInfo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo.setErrorMessage("An error occurred");
        reportRequestInfo.setFileDownloadLink("File Download Link");
        reportRequestInfo.setFileName("foo.txt");
        reportRequestInfo.setId("42");
        reportRequestInfo.setStatus("Status");
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.updateReportRequest(Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<ReportRequestInfo>any())).thenThrow(new TooEarlyRequestException("An error occurred"));
        when(reportingDAL.addReportRequest(Mockito.<ReportRequestInfo>any())).thenReturn(reportRequestInfo);
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        AuditDAL auditDAL = mock(AuditDAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);
        ReportingServiceImpl reportingServiceImpl = new ReportingServiceImpl(policyDAL, reportingDAL, endorsementDAL,
                auditDAL, invoiceSAL, brokerageHubSAL, auditSAL, userSAL, xlsxWriter, eventPublisher, storageService,
                "s3://bucket-name/object-key", 1, 8080);

        ReportRequestDTO reportRequestDTO = new ReportRequestDTO();
        reportRequestDTO.setEndDate(LocalDate.of(1970, 1, 1));
        reportRequestDTO.setStartDate(LocalDate.of(1970, 1, 1));
        reportRequestDTO.setReportingType(ReportingType.BORDEREAU);

        // Act and Assert
        assertThrows(TooEarlyRequestException.class,
                () -> reportingServiceImpl.createReportRequest(reportRequestDTO, "foo", "foo"));
        verify(reportingDAL).addReportRequest(isA(ReportRequestInfo.class));
        verify(reportingDAL).updateReportRequest(eq("42"), eq("foo"), isA(ReportRequestInfo.class));
    }

    /**
     * Method under test:
     * {@link ReportingServiceImpl#createReportRequest(ReportRequestDTO, String, String)}
     */
    @Test
    void testCreateReportRequest5() {


        // Arrange
        ReportRequestInfo reportRequestInfo = new ReportRequestInfo();
        reportRequestInfo.setClientId("42");
        reportRequestInfo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo.setErrorMessage("An error occurred");
        reportRequestInfo.setFileDownloadLink("File Download Link");
        reportRequestInfo.setFileName("foo.txt");
        reportRequestInfo.setId("42");
        reportRequestInfo.setStatus("Status");
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.updateReportRequest(Mockito.<String>any(), Mockito.<String>any(),
                Mockito.<ReportRequestInfo>any())).thenThrow(new TooEarlyRequestException("An error occurred"));
        when(reportingDAL.addReportRequest(Mockito.<ReportRequestInfo>any())).thenReturn(reportRequestInfo);
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);
        ReportingServiceImpl reportingServiceImpl = new ReportingServiceImpl(policyDAL, reportingDAL, endorsementDAL,
                auditDAL, invoiceSAL, brokerageHubSAL, auditSAL, userSAL, xlsxWriter, eventPublisher, storageService,
                "s3://bucket-name/object-key", 1, 8080);

        ReportRequestDTO reportRequestDTO = new ReportRequestDTO();
        reportRequestDTO.setEndDate(LocalDate.of(1970, 1, 1));
        reportRequestDTO.setStartDate(LocalDate.of(1970, 1, 1));
        reportRequestDTO.setReportingType(ReportingType.ASU_AGING);

        // Act and Assert
        assertThrows(TooEarlyRequestException.class,
                () -> reportingServiceImpl.createReportRequest(reportRequestDTO, "foo", "foo"));
        verify(reportingDAL).addReportRequest(isA(ReportRequestInfo.class));
        verify(reportingDAL).updateReportRequest(eq("42"), eq("foo"), isA(ReportRequestInfo.class));
    }

    /**
     * Method under test:
     * {@link ReportingServiceImpl#getReportRequestInfoById(String, String)}
     */
    @Test
    void testGetReportRequestInfoById() {


        // Arrange
        ReportRequestInfo reportRequestInfo = new ReportRequestInfo();
        reportRequestInfo.setClientId("42");
        reportRequestInfo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo.setErrorMessage("An error occurred");
        reportRequestInfo.setFileDownloadLink("File Download Link");
        reportRequestInfo.setFileName("foo.txt");
        reportRequestInfo.setId("42");
        reportRequestInfo.setStatus("Status");
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.getReportRequestById(Mockito.<String>any(), Mockito.<String>any())).thenReturn(reportRequestInfo);
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);

        // Act
        ReportRequestInfoDTO actualReportRequestInfoById = (new ReportingServiceImpl(policyDAL, reportingDAL,
                endorsementDAL, auditDAL, invoiceSAL, brokerageHubSAL, auditSAL,userSAL, xlsxWriter, eventPublisher, storageService,
                "s3://bucket-name/object-key", 1, 8080)).getReportRequestInfoById("42", "42");

        // Assert
        verify(reportingDAL).getReportRequestById("42", "42");
        assertEquals("1970-01-01", actualReportRequestInfoById.getCreatedAt().toLocalDate().toString());
        assertEquals("42", actualReportRequestInfoById.getId());
        assertEquals("An error occurred", actualReportRequestInfoById.getErrorMessage());
        assertEquals("File Download Link", actualReportRequestInfoById.getFileDownloadLink());
        assertEquals("Jan 1, 2020 8:00am GMT+0100", actualReportRequestInfoById.getCreatedBy());
        assertEquals("Status", actualReportRequestInfoById.getStatus());
        assertEquals("foo.txt", actualReportRequestInfoById.getFileName());
    }

    /**
     * Method under test:
     * {@link ReportingServiceImpl#getReportRequestInfoById(String, String)}
     */
    @Test
    void testGetReportRequestInfoById2() {


        // Arrange
        ReportRequestInfo reportRequestInfo = mock(ReportRequestInfo.class);
        when(reportRequestInfo.getId()).thenThrow(new TooEarlyRequestException("An error occurred"));
        when(reportRequestInfo.getStatus()).thenReturn("Status");
        doNothing().when(reportRequestInfo).setClientId(Mockito.<String>any());
        doNothing().when(reportRequestInfo).setCreatedAt(Mockito.<LocalDateTime>any());
        doNothing().when(reportRequestInfo).setCreatedBy(Mockito.<String>any());
        doNothing().when(reportRequestInfo).setErrorMessage(Mockito.<String>any());
        doNothing().when(reportRequestInfo).setFileDownloadLink(Mockito.<String>any());
        doNothing().when(reportRequestInfo).setFileName(Mockito.<String>any());
        doNothing().when(reportRequestInfo).setId(Mockito.<String>any());
        doNothing().when(reportRequestInfo).setStatus(Mockito.<String>any());
        reportRequestInfo.setClientId("42");
        reportRequestInfo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo.setErrorMessage("An error occurred");
        reportRequestInfo.setFileDownloadLink("File Download Link");
        reportRequestInfo.setFileName("foo.txt");
        reportRequestInfo.setId("42");
        reportRequestInfo.setStatus("Status");
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.getReportRequestById(Mockito.<String>any(), Mockito.<String>any())).thenReturn(reportRequestInfo);
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);

        // Act and Assert
        assertThrows(TooEarlyRequestException.class,
                () -> (new ReportingServiceImpl(policyDAL, reportingDAL, endorsementDAL, auditDAL, invoiceSAL, brokerageHubSAL,
                        auditSAL, userSAL,xlsxWriter, eventPublisher, storageService, "s3://bucket-name/object-key", 1, 8080))
                        .getReportRequestInfoById("42", "42"));
        verify(reportingDAL).getReportRequestById("42", "42");
        verify(reportRequestInfo).getId();
        verify(reportRequestInfo, atLeast(1)).getStatus();
        verify(reportRequestInfo).setClientId("42");
        verify(reportRequestInfo).setCreatedAt(isA(LocalDateTime.class));
        verify(reportRequestInfo).setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        verify(reportRequestInfo).setErrorMessage("An error occurred");
        verify(reportRequestInfo).setFileDownloadLink("File Download Link");
        verify(reportRequestInfo).setFileName("foo.txt");
        verify(reportRequestInfo).setId("42");
        verify(reportRequestInfo).setStatus("Status");
    }

    /**
     * Method under test:
     * {@link ReportingServiceImpl#getReportRequestInfoById(String, String)}
     */
    @Test
    void testGetReportRequestInfoById3() {


        // Arrange
        ReportRequestInfo reportRequestInfo = mock(ReportRequestInfo.class);
        when(reportRequestInfo.getStatus()).thenReturn("IN_PROGRESS");
        doNothing().when(reportRequestInfo).setClientId(Mockito.<String>any());
        doNothing().when(reportRequestInfo).setCreatedAt(Mockito.<LocalDateTime>any());
        doNothing().when(reportRequestInfo).setCreatedBy(Mockito.<String>any());
        doNothing().when(reportRequestInfo).setErrorMessage(Mockito.<String>any());
        doNothing().when(reportRequestInfo).setFileDownloadLink(Mockito.<String>any());
        doNothing().when(reportRequestInfo).setFileName(Mockito.<String>any());
        doNothing().when(reportRequestInfo).setId(Mockito.<String>any());
        doNothing().when(reportRequestInfo).setStatus(Mockito.<String>any());
        reportRequestInfo.setClientId("42");
        reportRequestInfo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo.setErrorMessage("An error occurred");
        reportRequestInfo.setFileDownloadLink("File Download Link");
        reportRequestInfo.setFileName("foo.txt");
        reportRequestInfo.setId("42");
        reportRequestInfo.setStatus("Status");
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.getReportRequestById(Mockito.<String>any(), Mockito.<String>any())).thenReturn(reportRequestInfo);
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);

        // Act and Assert
        assertThrows(TooEarlyRequestException.class,
                () -> (new ReportingServiceImpl(policyDAL, reportingDAL, endorsementDAL, auditDAL, invoiceSAL, brokerageHubSAL,
                        auditSAL, userSAL,xlsxWriter, eventPublisher, storageService, "s3://bucket-name/object-key", 1, 8080))
                        .getReportRequestInfoById("42", "42"));
        verify(reportingDAL).getReportRequestById("42", "42");
        verify(reportRequestInfo, atLeast(1)).getStatus();
        verify(reportRequestInfo).setClientId("42");
        verify(reportRequestInfo).setCreatedAt(isA(LocalDateTime.class));
        verify(reportRequestInfo).setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        verify(reportRequestInfo).setErrorMessage("An error occurred");
        verify(reportRequestInfo).setFileDownloadLink("File Download Link");
        verify(reportRequestInfo).setFileName("foo.txt");
        verify(reportRequestInfo).setId("42");
        verify(reportRequestInfo).setStatus("Status");
    }

    /**
     * Method under test:
     * {@link ReportingServiceImpl#getReportRequestInfoById(String, String)}
     */
    @Test
    void testGetReportRequestInfoById4() {


        // Arrange
        ReportRequestInfo reportRequestInfo = mock(ReportRequestInfo.class);
        when(reportRequestInfo.getFileName()).thenThrow(new TooEarlyRequestException("An error occurred"));
        when(reportRequestInfo.getStatus()).thenReturn("COMPLETED");
        doNothing().when(reportRequestInfo).setClientId(Mockito.any());
        doNothing().when(reportRequestInfo).setCreatedAt(Mockito.any());
        doNothing().when(reportRequestInfo).setCreatedBy(Mockito.any());
        doNothing().when(reportRequestInfo).setErrorMessage(Mockito.any());
        doNothing().when(reportRequestInfo).setFileDownloadLink(Mockito.any());
        doNothing().when(reportRequestInfo).setFileName(Mockito.any());
        doNothing().when(reportRequestInfo).setId(Mockito.any());
        doNothing().when(reportRequestInfo).setStatus(Mockito.any());
        reportRequestInfo.setClientId("42");
        reportRequestInfo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo.setErrorMessage("An error occurred");
        reportRequestInfo.setFileDownloadLink("File Download Link");
        reportRequestInfo.setFileName("foo.txt");
        reportRequestInfo.setId("42");
        reportRequestInfo.setStatus("Status");
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.getReportRequestById(Mockito.any(), Mockito.any())).thenReturn(reportRequestInfo);
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);

        // Act and Assert
        assertThrows(TooEarlyRequestException.class,
                () -> (new ReportingServiceImpl(policyDAL, reportingDAL, endorsementDAL, auditDAL, invoiceSAL, brokerageHubSAL,
                        auditSAL, userSAL, xlsxWriter, eventPublisher, storageService, "s3://bucket-name/object-key", 1, 8080))
                        .getReportRequestInfoById("42", "42"));
        verify(reportingDAL).getReportRequestById("42", "42");
        verify(reportRequestInfo).getFileName();
        verify(reportRequestInfo, atLeast(1)).getStatus();
        verify(reportRequestInfo).setClientId("42");
        verify(reportRequestInfo).setCreatedAt(isA(LocalDateTime.class));
        verify(reportRequestInfo).setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        verify(reportRequestInfo).setErrorMessage("An error occurred");
        verify(reportRequestInfo).setFileDownloadLink("File Download Link");
        verify(reportRequestInfo).setFileName("foo.txt");
        verify(reportRequestInfo).setId("42");
        verify(reportRequestInfo).setStatus("Status");
    }

    /**
     * Method under test:
     * {@link ReportingServiceImpl#getPairOfTotalCountAndReportRequestInfoList(PageRequest, String)}
     */
    @Test
    void testGetPairOfTotalCountAndReportRequestInfoList() {


        // Arrange
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.getPairOfTotalCountAndReportRequestInfoList(Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn(new ImmutablePair<>(2L, new ArrayList<>()));
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);

        // Act
        Pair<Long, List<ReportRequestInfoDTO>> actualPairOfTotalCountAndReportRequestInfoList = (new ReportingServiceImpl(
                policyDAL, reportingDAL, endorsementDAL, auditDAL, invoiceSAL, brokerageHubSAL, auditSAL, userSAL, xlsxWriter, eventPublisher,
                storageService, "s3://bucket-name/object-key", 1, 8080))
                .getPairOfTotalCountAndReportRequestInfoList(null, "42");

        // Assert
        verify(reportingDAL).getPairOfTotalCountAndReportRequestInfoList(isNull(), eq("42"), isA(LocalDate.class));
        List<ReportRequestInfoDTO> expectedRight = actualPairOfTotalCountAndReportRequestInfoList.getValue();
        assertSame(expectedRight, actualPairOfTotalCountAndReportRequestInfoList.getRight());
    }

    /**
     * Method under test:
     * {@link ReportingServiceImpl#getPairOfTotalCountAndReportRequestInfoList(PageRequest, String)}
     */
    @Test
    void testGetPairOfTotalCountAndReportRequestInfoList2() {

        // Arrange
        ReportRequestInfo reportRequestInfo = new ReportRequestInfo();
        reportRequestInfo.setClientId("42");
        reportRequestInfo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo.setErrorMessage("An error occurred");
        reportRequestInfo.setFileDownloadLink("Entering into : ");
        reportRequestInfo.setFileName("foo.txt");
        reportRequestInfo.setId("42");
        reportRequestInfo.setStatus("Entering into : ");

        ArrayList<ReportRequestInfo> reportRequestInfoList = new ArrayList<>();
        reportRequestInfoList.add(reportRequestInfo);
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.getPairOfTotalCountAndReportRequestInfoList(Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn(new ImmutablePair<>(2L, reportRequestInfoList));
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);

        // Act
        Pair<Long, List<ReportRequestInfoDTO>> actualPairOfTotalCountAndReportRequestInfoList = (new ReportingServiceImpl(
                policyDAL, reportingDAL, endorsementDAL, auditDAL, invoiceSAL, brokerageHubSAL, auditSAL,userSAL, xlsxWriter, eventPublisher,
                storageService, "s3://bucket-name/object-key", 1, 8080))
                .getPairOfTotalCountAndReportRequestInfoList(null, "42");

        // Assert
        verify(reportingDAL).getPairOfTotalCountAndReportRequestInfoList(isNull(), eq("42"), isA(LocalDate.class));
        List<ReportRequestInfoDTO> expectedRight = actualPairOfTotalCountAndReportRequestInfoList.getValue();
        assertSame(expectedRight, actualPairOfTotalCountAndReportRequestInfoList.getRight());
    }

    /**
     * Method under test:
     * {@link ReportingServiceImpl#getPairOfTotalCountAndReportRequestInfoList(PageRequest, String)}
     */
    @Test
    void testGetPairOfTotalCountAndReportRequestInfoList3() {


        // Arrange
        ReportRequestInfo reportRequestInfo = new ReportRequestInfo();
        reportRequestInfo.setClientId("42");
        reportRequestInfo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo.setErrorMessage("An error occurred");
        reportRequestInfo.setFileDownloadLink("Entering into : ");
        reportRequestInfo.setFileName("foo.txt");
        reportRequestInfo.setId("42");
        reportRequestInfo.setStatus("Entering into : ");

        ReportRequestInfo reportRequestInfo2 = new ReportRequestInfo();
        reportRequestInfo2.setClientId("Entering into : ");
        reportRequestInfo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo2.setCreatedBy("Entering into : ");
        reportRequestInfo2.setErrorMessage("Entering into : ");
        reportRequestInfo2.setFileDownloadLink(" Method params: ");
        reportRequestInfo2.setFileName("Entering into : ");
        reportRequestInfo2.setId("Entering into : ");
        reportRequestInfo2.setStatus(" Method params: ");

        ArrayList<ReportRequestInfo> reportRequestInfoList = new ArrayList<>();
        reportRequestInfoList.add(reportRequestInfo2);
        reportRequestInfoList.add(reportRequestInfo);
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.getPairOfTotalCountAndReportRequestInfoList(Mockito.<PageRequest>any(), Mockito.<String>any(),
                Mockito.<LocalDate>any())).thenReturn(new ImmutablePair<>(2L, reportRequestInfoList));
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);

        // Act
        Pair<Long, List<ReportRequestInfoDTO>> actualPairOfTotalCountAndReportRequestInfoList = (new ReportingServiceImpl(
                policyDAL, reportingDAL, endorsementDAL, auditDAL, invoiceSAL, brokerageHubSAL, auditSAL, userSAL,xlsxWriter, eventPublisher,
                storageService, "s3://bucket-name/object-key", 1, 8080))
                .getPairOfTotalCountAndReportRequestInfoList(null, "42");

        // Assert
        verify(reportingDAL).getPairOfTotalCountAndReportRequestInfoList(isNull(), eq("42"), isA(LocalDate.class));
        List<ReportRequestInfoDTO> expectedRight = actualPairOfTotalCountAndReportRequestInfoList.getValue();
        assertSame(expectedRight, actualPairOfTotalCountAndReportRequestInfoList.getRight());
    }

    /**
     * Method under test: {@link ReportingServiceImpl#deleteReports()}
     */
    @Test
    void testDeleteReports() {


        // Arrange
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.getReportRequestInfoListBeforeDate(Mockito.<PageRequest>any(), Mockito.<String>any(),
                Mockito.<LocalDate>any())).thenReturn(new ArrayList<>());
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);

        // Act
        (new ReportingServiceImpl(policyDAL, reportingDAL, endorsementDAL, auditDAL, invoiceSAL, brokerageHubSAL, auditSAL, userSAL,xlsxWriter,
                eventPublisher, storageService, "s3://bucket-name/object-key", 1, 8080)).deleteReports();

        // Assert
        verify(reportingDAL).getReportRequestInfoListBeforeDate(isA(PageRequest.class), isNull(), isA(LocalDate.class));
    }

    /**
     * Method under test: {@link ReportingServiceImpl#deleteReports()}
     */
    @Test
    void testDeleteReports2() {


        // Arrange
        ReportRequestInfo reportRequestInfo = new ReportRequestInfo();
        reportRequestInfo.setClientId("42");
        reportRequestInfo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo.setErrorMessage("An error occurred");
        reportRequestInfo.setFileDownloadLink("fetched {} expired reports that were before date '{}'");
        reportRequestInfo.setFileName("foo.txt");
        reportRequestInfo.setId("42");
        reportRequestInfo.setStatus("fetched {} expired reports that were before date '{}'");

        ArrayList<ReportRequestInfo> reportRequestInfoList = new ArrayList<>();
        reportRequestInfoList.add(reportRequestInfo);
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.getReportRequestInfoListBeforeDate(Mockito.<PageRequest>any(), Mockito.<String>any(),
                Mockito.<LocalDate>any())).thenReturn(reportRequestInfoList);
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);

        // Act
        (new ReportingServiceImpl(policyDAL, reportingDAL, endorsementDAL, auditDAL, invoiceSAL, brokerageHubSAL, auditSAL,userSAL, xlsxWriter,
                eventPublisher, storageService, "s3://bucket-name/object-key", 1, 8080)).deleteReports();

        // Assert
        verify(reportingDAL).getReportRequestInfoListBeforeDate(isA(PageRequest.class), isNull(), isA(LocalDate.class));
    }

    /**
     * Method under test: {@link ReportingServiceImpl#deleteReports()}
     */
    @Test
    void testDeleteReports3() {


        // Arrange
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.getReportRequestInfoListBeforeDate(Mockito.<PageRequest>any(), Mockito.<String>any(),
                Mockito.<LocalDate>any())).thenThrow(new TooEarlyRequestException("An error occurred"));
        PolicyDAL policyDAL = mock(PolicyDAL.class);
        EndorsementDAL endorsementDAL = mock(EndorsementDAL.class);
        InvoiceSAL invoiceSAL = mock(InvoiceSAL.class);
        BrokerageHubSAL brokerageHubSAL = mock(BrokerageHubSAL.class);
        AuditSAL auditSAL = mock(AuditSAL.class);
        XlsxWriter xlsxWriter = mock(XlsxWriter.class);
        ApplicationEventPublisher eventPublisher = mock(ApplicationEventPublisher.class);

        // Act
        (new ReportingServiceImpl(policyDAL, reportingDAL, endorsementDAL, auditDAL, invoiceSAL, brokerageHubSAL, auditSAL, userSAL,xlsxWriter,
                eventPublisher, storageService, "s3://bucket-name/object-key", 1, 8080)).deleteReports();

        // Assert
        verify(reportingDAL).getReportRequestInfoListBeforeDate(isA(PageRequest.class), isNull(), isA(LocalDate.class));
    }

    /**
     * Method under test: {@link ReportingServiceImpl#deleteReports()}
     */
    @Test
    void testDeleteReports4() {
        // Arrange
        ReportRequestInfo reportRequestInfo = new ReportRequestInfo();
        reportRequestInfo.setClientId("42");
        reportRequestInfo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay());
        reportRequestInfo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        reportRequestInfo.setErrorMessage("An error occurred");
        reportRequestInfo.setFileDownloadLink("fetched {} expired reports that were before date '{}'");
        reportRequestInfo.setFileName("foo.txt");
        reportRequestInfo.setId("42");
        reportRequestInfo.setStatus("fetched {} expired reports that were before date '{}'");

        ArrayList<ReportRequestInfo> reportRequestInfoList = new ArrayList<>();
        reportRequestInfoList.add(reportRequestInfo);
        ReportingDALImpl reportingDAL = mock(ReportingDALImpl.class);
        when(reportingDAL.getReportRequestInfoListBeforeDate(Mockito.<PageRequest>any(), Mockito.<String>any(),
                Mockito.<LocalDate>any())).thenReturn(reportRequestInfoList);

        // Act
        (new ReportingServiceImpl(mock(PolicyDAL.class), reportingDAL, mock(EndorsementDAL.class), auditDAL, mock(InvoiceSAL.class),
                mock(BrokerageHubSAL.class), mock(AuditSAL.class), userSAL, mock(XlsxWriter.class), mock(ApplicationEventPublisher.class), null,
                "s3://bucket-name/object-key", 1, 8080)).deleteReports();

        // Assert
        verify(reportingDAL).getReportRequestInfoListBeforeDate(isA(PageRequest.class), isNull(), isA(LocalDate.class));
    }

}
