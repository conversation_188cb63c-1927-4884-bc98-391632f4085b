package com.concirrus.reporting.sal.impl;

import com.concirrus.reporting.dto.SearchOfficeRequest;
import com.concirrus.reporting.model.exception.BadRequestException;
import com.concirrus.reporting.sal.BrokerageHubSAL;
import com.concirrus.reporting.utils.LoggingUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.concirrus.reporting.constant.DBConstant.PAGE_NUMBER;
import static com.concirrus.reporting.constant.DBConstant.PAGE_SIZE;
import static com.concirrus.reporting.constant.RequestConstant.CLIENT_ID;

@Slf4j
@Component
public class BrokerageHubSALImpl implements BrokerageHubSAL {
    private final String brokerageHubSvcBaseURL;
    private final RestTemplate restTemplate;

    public BrokerageHubSALImpl(@Value("${external.endpoints.brokerage-hub-service.baseUrl}") String brokerageHubSvcBaseURL, RestTemplate restTemplate) {
        this.brokerageHubSvcBaseURL = brokerageHubSvcBaseURL;
        this.restTemplate = restTemplate;
    }

    @Override
    public List<Map<String, String>> getOfficesByIds(Collection<String> officeIds, int pageNumber, int pageSize, String clientId) {
        log.info(LoggingUtils.logMethodEntry(officeIds, pageNumber, pageSize, clientId));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add(CLIENT_ID, clientId);

        SearchOfficeRequest searchOfficeRequest = new SearchOfficeRequest();
        searchOfficeRequest.setOfficeIds(officeIds);

        HttpEntity<SearchOfficeRequest> requestEntity = new HttpEntity<>(searchOfficeRequest, headers);

        try {
            // Build the URL with UriComponentsBuilder
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(brokerageHubSvcBaseURL + "/office/search")
                    .queryParam(PAGE_NUMBER, pageNumber)
                    .queryParam(PAGE_SIZE, pageSize);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(builder.toUriString(), HttpMethod.POST, requestEntity, new ParameterizedTypeReference<>() {
            });

            Map<String, Object> questResponse = response.getBody();

            if (MapUtils.isEmpty(questResponse)) {
                log.info("Response is null or empty");
                return Collections.emptyList();
            }

            List<Object> errors = (List<Object>) questResponse.get("errors");

            if (CollectionUtils.isNotEmpty(errors)) {
                throw new BadRequestException(errors.get(0).toString());
            }

            Map<String, Object> data = (Map<String, Object>) questResponse.get("data");
            List<Map<String, String>> offices = (List<Map<String, String>>) data.get("offices");

            if (CollectionUtils.isEmpty(offices)) {
                log.info("Not found any offices for ids : {}", officeIds);
                return Collections.emptyList();
            }

            log.info("fetched offices size:{}", offices.size());
            return offices;
        } catch (Exception e) {
            log.error("Exception occurred while fetching the offices by ids", e);
            throw new BadRequestException(e.getMessage());
        }
    }

}
