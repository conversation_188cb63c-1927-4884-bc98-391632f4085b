package com.concirrus.reporting.sal.impl;

import com.concirrus.reporting.dto.SearchInvoiceInputDTO;
import com.concirrus.reporting.model.exception.BadRequestException;
import com.concirrus.reporting.sal.InvoiceSAL;
import com.concirrus.reporting.utils.LoggingUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.concirrus.reporting.constant.RequestConstant.CLIENT_ID;


@Slf4j
@Component

public class InvoiceSALImpl implements InvoiceSAL {

    private final String invoiceDataSvcBaseURL;
    private final RestTemplate restTemplate;
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public InvoiceSALImpl(@Value("${external.endpoints.invoice-data-service.baseUrl}") String invoiceDataSvcBaseURL, RestTemplate restTemplate) {
        this.invoiceDataSvcBaseURL = invoiceDataSvcBaseURL;
        this.restTemplate = restTemplate;
    }

    public List<Map<String, String>> searchInvoicesBy(List<String> invoiceIds, int pageNumber, int pageSize, String clientId) {
        log.info(LoggingUtils.logMethodEntry(invoiceIds, pageNumber, pageSize, clientId));
        try {
            SearchInvoiceInputDTO searchInvoiceInputDTO = new SearchInvoiceInputDTO();
            searchInvoiceInputDTO.setIds(invoiceIds);
            searchInvoiceInputDTO.setPage(pageNumber);
            searchInvoiceInputDTO.setSize(pageSize);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add(CLIENT_ID, clientId);

            HttpEntity<SearchInvoiceInputDTO> requestEntity = new HttpEntity<>(searchInvoiceInputDTO, headers);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(invoiceDataSvcBaseURL, HttpMethod.POST, requestEntity, new ParameterizedTypeReference<>() {
            });

            Map<String, Object> questResponse = response.getBody();

            if(MapUtils.isEmpty(questResponse)){
                return new ArrayList<>();
            }

            List<Object> errors = (List<Object>) questResponse.get("errors");

            if (CollectionUtils.isNotEmpty(errors)) {
                throw new BadRequestException(errors.get(0).toString());
            }

            List<Map<String, String>> items = (List<Map<String, String>>) questResponse.get("items");

            log.info("Invoice items size:{}", items.size());
            return items;
        } catch (Exception e) {
            log.error("Exception occurred while creating an invoice", e);
            throw new BadRequestException(e.getMessage());
        }
    }
}
