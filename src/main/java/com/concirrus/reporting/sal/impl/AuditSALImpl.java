package com.concirrus.reporting.sal.impl;

import com.concirrus.reporting.dto.AuditPolicyRequestDTO;
import com.concirrus.reporting.dto.PagedResponse;
import com.concirrus.reporting.dto.PolicySubmissionAuditDTO;
import com.concirrus.reporting.model.exception.ApplicationException;
import com.concirrus.reporting.sal.AuditSAL;
import com.concirrus.reporting.utils.LoggingUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;

import static com.concirrus.reporting.constant.DBConstant.*;
import static com.concirrus.reporting.constant.RequestConstant.CLIENT_ID;


@Slf4j
@Component
@RequiredArgsConstructor
public class AuditSALImpl implements AuditSAL {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    @Value("${external.endpoints.audit-service.baseUrl}")
    private String auditSvcBaseURL;

    @Override
    public List<PolicySubmissionAuditDTO> getRecentAuditPolicesByPolicyIds(List<String> policyIds, String reason, Integer pageNumber, Integer pageSize, String sortOrder, String sortBy, String clientId) {
        log.info(LoggingUtils.logMethodEntry(reason, pageNumber, pageSize, sortOrder, sortBy, clientId));
        try {
            // Build the URL with UriComponentsBuilder
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(auditSvcBaseURL + "/policies")
                    .queryParam(PAGE_NUMBER, pageNumber)
                    .queryParam(PAGE_SIZE, pageSize)
                    .queryParam(SORT_ORDER, sortOrder)
                    .queryParam(SORT_BY, sortBy);

            // Constructing the request body
            AuditPolicyRequestDTO requestBody = new AuditPolicyRequestDTO();
            requestBody.setPolicyIds(policyIds);
            requestBody.setReason(reason);

            // Constructing request headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add(CLIENT_ID, clientId);

            ResponseEntity<PagedResponse> responseEntity = restTemplate.exchange(
                    builder.toUriString(),
                    HttpMethod.POST,
                    new HttpEntity<>(requestBody, headers),
                    PagedResponse.class);

            PagedResponse questResponse = responseEntity.getBody();

            TypeReference<List<PolicySubmissionAuditDTO>> mapType = new TypeReference<>() {
            };

            String jsonString = objectMapper.writeValueAsString(questResponse != null ? questResponse.getData() : StringUtils.EMPTY);
            List<PolicySubmissionAuditDTO> auditData = objectMapper.readValue(jsonString, mapType);
            log.info("fetched {} audit data record", auditData.size());

           return auditData;
        } catch (Exception ex) {
            log.error("Error in fetching policy audit for policy Ids size : {}", policyIds.size());
            throw new ApplicationException(ex.getMessage());
        }
    }
}
