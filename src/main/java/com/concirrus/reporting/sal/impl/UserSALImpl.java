package com.concirrus.reporting.sal.impl;

import com.concirrus.reporting.model.exception.ApplicationException;
import com.concirrus.reporting.model.exception.BadRequestException;
import com.concirrus.reporting.sal.UserSAL;
import com.concirrus.reporting.utils.LoggingUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.concirrus.reporting.constant.RequestConstant.CLIENT_ID;
import static org.apache.commons.lang3.StringUtils.SPACE;

@Slf4j
@Component
@RequiredArgsConstructor
public class UserSALImpl implements UserSAL {

    private final RestTemplate restTemplate;
    @Value("${external.endpoints.access-management-service.baseUrl}")
    private String accessManagementBaseURL;


    @Override
    public List<Map<String, Object>> getUserInfoByIds(Collection<String> userIds, String clientId) {
        log.info(LoggingUtils.logMethodEntry(userIds, clientId));

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(CLIENT_ID, clientId);
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Collection<String>> requestEntity = new HttpEntity<>(userIds, httpHeaders);

        try {
            // Build the URL with UriComponentsBuilder
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(accessManagementBaseURL + "/api/v1/users/multiple");

            // Make the request with the built URL
            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    builder.toUriString(),
                    HttpMethod.POST,
                    requestEntity,
                    new ParameterizedTypeReference<>() {
                    }
            );

            Map<String, Object> questResponse = response.getBody();

            if (MapUtils.isEmpty(questResponse)) {
                log.info("The response from the access management service is either null or empty");
                return Collections.emptyList();
            }

            String error = (String) questResponse.get("error");
            String message = (String) questResponse.get("message");

            if (StringUtils.isNotBlank(error)) {
                log.error(error, message);
                throw new BadRequestException(error + SPACE + message);
            }

            List<Map<String, Object>> data = (List<Map<String, Object>>) questResponse.get("result");

            log.info("Fetched '{}' user info records", data.size());
            return data;
        } catch (Exception e) {
            log.error("Exception occurred while fetching the user info by ids", e);
            throw new ApplicationException(e.getMessage());
        }
    }
}
