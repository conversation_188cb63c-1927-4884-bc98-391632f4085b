package com.concirrus.reporting.controller;


import com.concirrus.reporting.annotation.UserRoleValidation;
import com.concirrus.reporting.dto.PagedResponse;
import com.concirrus.reporting.dto.ReportRequestDTO;
import com.concirrus.reporting.dto.ReportRequestInfoDTO;
import com.concirrus.reporting.dto.SubmissionRequestDTO;
import com.concirrus.reporting.enums.ReportingType;
import com.concirrus.reporting.service.ReportingService;
import com.concirrus.reporting.utils.DateValidator;
import com.concirrus.reporting.utils.LoggingUtils;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.List;

import static com.concirrus.reporting.constant.DBConstant.CREATED_AT;
import static com.concirrus.reporting.constant.RequestConstant.*;
import static com.concirrus.reporting.enums.ReportingType.AVIATION_BORDEREAU;
import static com.concirrus.reporting.enums.ReportingType.AVIATION_SUBMISSION;

@Slf4j
@RestController
@RequestMapping("/reports")
@Validated
public class ReportingController {
    private final ReportingService reportingService;

    @Autowired
    public ReportingController(ReportingService reportingService) {
        this.reportingService = reportingService;
    }

    /**
     * Handles a POST request to create a report.
     *
     * @param reportRequestDTO The data transfer object containing report request details.
     * @param userId           The user ID provided in the request header.
     * @param clientId         The client ID provided in the request header.
     * @return An APIResponse object containing information about the created report request.
     */
    @PostMapping("/requests")
    @ResponseStatus(HttpStatus.CREATED)
    public PagedResponse createReportRequest(@RequestBody @Valid ReportRequestDTO reportRequestDTO,
                                             @RequestHeader(value = USER_ID) String userId,
                                             @RequestHeader(value = CLIENT_ID) String clientId) {
        log.info(LoggingUtils.logMethodEntry(reportRequestDTO, clientId));
        Instant start = Instant.now();

        // validate dates
        if(List.of(ReportingType.BORDEREAU, ReportingType.ACCOUNT_RECEIVABLE_AGING, AVIATION_BORDEREAU, AVIATION_SUBMISSION).contains(reportRequestDTO.getReportingType())){
            DateValidator.validateDate(reportRequestDTO.getStartDate(), reportRequestDTO.getEndDate());
        }
        if (reportRequestDTO.getReportingType().equals(ReportingType.ALL_DATA)) {
            DateValidator.validateDateAllData(reportRequestDTO.getStartDate(), reportRequestDTO.getEndDate());
        }

        PagedResponse response = new PagedResponse();
        final String requestId = reportingService.createReportRequest(reportRequestDTO, userId, clientId);
        response.setData(String.format("Request submitted successfully with id : %s", requestId));

        log.info(LoggingUtils.logMethodExitWithMemoryAndDuration(start));
        return response;
    }

    /**
     * Handles a GET request to retrieve a list of report request information.
     *
     * @param pageNumber The page number for pagination (default is 0).
     * @param pageSize   The page size for pagination (default is 10).
     * @param clientId   The client ID provided in the request header.
     * @return An APIResponse object containing a list of report request information.
     */
    @GetMapping("/requests")
    @UserRoleValidation(action = "read", entity = "report")
    public PagedResponse getReportRequestInfoList(@RequestParam(defaultValue = "0") int pageNumber,
                                                  @RequestParam(defaultValue = "10") @Min(PAGE_SIZE_MIN) @Max(PAGE_SIZE_MAX) int pageSize,
                                                  @RequestParam(defaultValue = "ASC") Sort.Direction sortOrder,
                                                  @RequestParam(defaultValue = CREATED_AT) String sortBy,
                                                  @RequestHeader(value = CLIENT_ID) String clientId) {
        log.info(LoggingUtils.logMethodEntry(pageNumber, pageSize, clientId));
        Instant start = Instant.now();

        PageRequest pageRequest = PageRequest.of(pageNumber, pageSize, Sort.by(sortOrder, sortBy));
        PagedResponse response = new PagedResponse();

        Pair<Long, List<ReportRequestInfoDTO>> data = reportingService.getPairOfTotalCountAndReportRequestInfoList(pageRequest, clientId);

        response.setData(data.getRight());
        response.setTotalItems(data.getLeft());

        log.info(LoggingUtils.logMethodExitWithMemoryAndDuration(start));
        return response;
    }

    /**
     * Handles a GET request to retrieve information about a specific report request.
     *
     * @param requestId The ID of the report request to retrieve.
     * @param clientId  The client ID provided in the request header.
     * @return An APIResponse object containing information about the specified report request.
     */
    @GetMapping("/requests/{requestId}")
    @UserRoleValidation(action = "read", entity = "report")
    public PagedResponse getReportRequestInfo(@PathVariable String requestId, @RequestHeader(value = CLIENT_ID) String clientId) {
        log.info(LoggingUtils.logMethodEntry(requestId, clientId));
        Instant start = Instant.now();

        PagedResponse response = new PagedResponse();
        response.setData(reportingService.getReportRequestInfoById(requestId, clientId));

        log.info(LoggingUtils.logMethodExitWithMemoryAndDuration(start));
        return response;
    }

    /**
     * Handles HTTP DELETE request to delete reports.
     * Delete reports from both Amazon S3 and MongoDB that are older than certain days.
     * Returns HTTP status code 204 (NO_CONTENT) upon successful completion.
     */
    @DeleteMapping
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteReports() {
        log.info(LoggingUtils.logMethodEntry());
        Instant start = Instant.now();
        reportingService.deleteReports();
        log.info(LoggingUtils.logMethodExitWithMemoryAndDuration(start));
    }
}
