package com.concirrus.reporting.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ReportConstant {

    public static final String POLICY_INCEPTION_DATE = "policyInceptionDate";
    public static final String POLICY_BOUND_DATE = "policyBoundDate";
    public static final String POLICY_EXPIRATION_DATE = "policyExpirationDate";
    public static final String POLICY_NUMBER = "policyNumber";
    public static final String INSURED_INFO = "insuredInfo";
    public static final String FIRST_NAME = "firstName";
    public static final String BROKER_INFO = "brokerInfo";
    public static final String INSURED_INFO_INSURED_FIRST_NAME = "insuredInfo.firstName";
    public static final String BROKER_INFO_NAME = "brokerInfo.name";
    public static final String BROKER_INFO_SENDER_NAME = "brokerInfo.senderName";
    public static final String NAME = "name";
    public static final String OFFICE_NAME = "officeName";
    public static final String DESCRIPTION_OF_OPERATIONS = "descriptionOfOperations";
    public static final String RISK_STATE = "riskState";
    public static final String PRODUCT_NAME = "productName";
    public static final String PRODUCT_TYPE = "productType";
    public static final String UNDERWRITER_NAME = "underwriterName";
    public static final String CARRIER = "carrier";
    public static final String PRODUCT_STATE = "productState";
    public static final String EXPOSURE_DETAILS = "exposureDetails";
    public static final String EXCESS = "Excess";
    public static final String EACH_OCCURRENCE_LIMIT_EXCESS = "eachOccurrenceLimitExcess";
    public static final String PRODUCTS_COMPLETED_OPERATIONS_AGGREGATE = "productsCompletedOperationsAggregate";

    public static final String OTHER_AGGREGATE_LIMIT = "otherAggregateLimit";
    public static final String POLICY_AGGREGATE_LIMIT = "policyAggregateLimit";

    public static final String EACH_OCCURRENCE_LIMIT_PRIMARY = "eachOccurrenceLimitPrimary";

    public static final String GENERAL_AGGREGATE_LIMIT = "generalAggregateLimit";

    public static final String PRODUCTS_COMPLETED_OPERATIONS_AGGREGATE_LIMIT = "productsCompletedOperationsAggregateLimit";

    public static final String PERSONAL_ADVERTISING_INJURY_LIMIT = "personalAdvertisingInjuryLimit";
    public static final String DAMAGE_TO_PREMISES_RENTED = "damageToPremisesRented";

    public static final String MEDICAL_PAYMENTS_LIMIT = "medicalPaymentsLimit";

    public static final String ATTACHMENT_TOTAL_LIMITS_INCL_GL = "attachmentTotalLimitsInclGL";

    public static final String NUMBER_OF_GENERAL_AGGREGATE_REINSTATEMENTS = "numberOfGeneralAggregateReinstatements";
    public static final String NYFTZ_CLASS = "nyftzClass";


    public static final String DEDUCTIBLE_DETAILS = "deductibleDetails";
    public static final String DEDUCTIBLE_AMOUNT = "deductibleAmount";

    public static final String SIR_AMOUNT = "sirAmount";
    public static final String RETENTION_TYPE = "retentionType";

    public static final String DEDUCTIBLE = "Deductible";

    public static final String D = "D";

    public static final String SELF_INSURED_RETENTION = "Self Insured Retention";

    public static final String S = "S";

    public static final String NOT_APPLICABLE = "N/A";

    public static final String RATE_DETAILS = "rateDetails";
    public static final String EXPOSURE_BASIS = "exposureBasis";

    public static final String EXPOSURE_AMOUNT_BY_EXPOSURE_BASIS = "exposureAmountByExposureBasis";

    public static final String RATE_BY_EXPOSURE_BASIS = "rateByExposureBasis";

    public static final String PREMIUM_DETAILS = "premiumDetails";

    public static final String ISO_CLASSIFICATION_CODE = "isoClassificationCode";
    public static final String TOTAL_AUTOS = "totalAutos";

    public static final String TOTAL_WRITTEN_PREMIUM = "totalWrittenPremium";

    public static final String TRIA_PREMIUM = "triaPremium";

    public static final String TOTAL_FEES = "totalFees";

    public static final String TOTAL_PREMIUM = "totalPremium";

    public static final String TOTAL_TECHNICAL_PREMIUM = "totalTechnicalPremium";

    public static final String SOLD_TO_TECHNICAL_PERCENTAGE = "soldToTechnicalPercentage";

    public static final String TREATY = "treaty";

    public static final String COMMISSION_DETAILS = "commissionDetails";

    public static final String PERCENTAGE_AS_STRING = "percentage";

    public static final String AMOUNT = "amount";

    public static final String TRANSACTION_TYPE = "transactionType";

    public static final String TRANSACTION_DATE = "transactionDate";

    public static final String RENEWAL = "Renewal";

    public static final String OTHER_NAMED_INSUREDS = "otherNamedInsureds";

    public static final String SENDER_NAME = "senderName";

    public static final String SENDER_EMAIL = "senderEmail";

    public static final String MAILING_ADDRESS = "mailingAddress";
    public static final String STREET = "street";

    public static final String CITY = "city";

    public static final String STATE = "state";

    public static final String PREVIOUS_STATE = "previousState";

    public static final String LAST_POLICY_STATE = "lastPolicyState";

    public static final String STATUS = "status";

    public static final String ZIP_CODE = "zipCode";

    public static final String INVOICE_NUMBER = "invoiceNumber";

    public static final String BILLED_DATE = "billedDate";

    public static final String DUE_DATE = "dueDate";

    public static final String PROJECT_DETAILS = "projectDetails";

    public static final String ALLOCATED_AMOUNT = "allocatedAmount";


    public static final String SLASH = "/";
    public static final String HYPHEN = "-";
    public static final String DOLLAR = "$";
    public static final String PERCENTAGE = "%";
    public static final String COMMA = ",";
    public static final String DOT = ".";
    public static final String ZERO_AS_STRING = "0";
    public static final String ENDORSEMENT = "Endorsement";
    public static final String BOUND = "BOUND";
    public static final String ISSUED = "ISSUED";
    public static final String DRAFT = "DRAFT";
    public static final String REINSTATEMENT = "REINSTATEMENT";
    public static final String CANCEL_REWRITE = "CANCEL_REWRITE";

    public static final String UNDERWRITER = "underWriter";
    public static final String RENEWAL_STATUS = "renewalStatus";
    public static final String INSURED_NAME = "insuredName";
    public static final String INSURED_STATE_ABBRIVATION = "insuredStateAbbrivation";
    public static final String INSURED_ADDRESS = "insuredAddress";
    public static final String INSURED_CITY = "insuredCity";
    public static final String INSURED_ZIP = "insuredZip";
    public static final String QUOTE_DATE = "quoteDate";
    public static final String ISSUED_DATE = "issuedDate";
    public static final String LEAD_POLICY = "leadPolicy";
    public static final String QS_PROPERTY = "qsProperty";
    public static final String PRODUCT = "product";
    public static final String AIRCRAFT_COUNT = "aircraftCount";
    public static final String AIRCRAFTS = "aircrafts";
    public static final String COMBINED_SINGLE_LIMIT = "combinedSingleLimit";
    public static final String INSURED_HULL_VALUE = "insuredHullValue";
    public static final String EXPOSURES_MANAGEMENT = "exposuresManagement";
    public static final String AUTOMATIC_INCR_FOR_HULL_VALUE = "automaticIncreaseForHullValueCoverageLimit";
    public static final String NEW_BUSINESS = "New Business";
    public static final String LEAD = "Lead";
    public static final String FOLLOW = "Follow";
}
