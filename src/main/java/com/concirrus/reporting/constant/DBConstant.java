package com.concirrus.reporting.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DBConstant {
    public  static final String MONGO_CLIENT_ID = "clientId";
    public static final String MONGO_ID = "_id";
    public static final String POLICY_START_DATE = "policyInceptionDate";
    public static final String POLICY_END_DATE = "policyExpirationDate";

    public static final String CREATED_AT = "createdAt";
    public static final  String ENDORSEMENT_POLICY_START_DATE = "updatedPolicyInfo.policyInceptionDate";

    public static final String IS_PREMIUM_PAID = "isPremiumPaid";

    public static final String ENDORSEMENT_EFFECTIVE_DATE = "endorsementEffectiveDate";

    public static final String ENDORSEMENT_NUMBER = "endorsementNumber";

    public static final String TRANSACTION_DESCRIPTION = "transactionDescription";

    public static final String ENDORSEMENT_SEQUENCE = "endorsementSequence";
    public static final String ENDORSEMENT_STATUS = "endorsementStatus";

    public static final String ENDORSEMENT_TYPE = "endorsementType";

    public static final String INVOICE_ID = "invoiceId";

    public static final String POLICY_ID = "policyId";
    public static final String PAYLOAD_POLICY_ID = "payload.policyId";

    public static final String PAYLOAD_PREMIUM_DETAILS = "payload.premiumDetails";
    public static final String PAYLOAD = "payload";
    public static final String PAYLOAD_STATE = "payload.state";
    public static final String PAYLOAD_IS_DELETED = "payload.isDeleted";
    public static final String PAYLOAD_UPDATED_AT = "payload.updatedAt";
    public static final String PAYLOAD_CLIENT_ID = "payload.clientId";
    public static final String OFFICE_ID = "officeId";
    public static final String BROKER_ID = "brokerId";
    public static final String INSURED_ID = "insuredId";
    public static final String RESPONSE_ID = "id";

    public static final String IS_DELETED= "isDeleted";

    public static final String UPDATED_POLICY_INFO = "updatedPolicyInfo";

    public static final String PREVIOUS_POLICY_INFO = "previousPolicyInfo";

    // tables
    public static final String INSURANCE_POLICY_COLLECTION = "policy_info";
    public static final String POLICY_ENDORSEMENT_COLLECTION = "policy_endorsement";

    public static final String AUDIT_POLICY_COLLECTION = "audit_policy";


    public static final String PAGE_NUMBER = "pageNumber";
    public static final String PAGE_SIZE = "pageSize";
    public static final String SORT_ORDER= "sortOrder";
    public static final String SORT_BY= "sortBy";

    public static final String INVOICE_BILLED_DATE = "invoiceBilledDate";
    public static final String INVOICE_DUE_DATE = "invoiceDueDate";
    public static final String INVOICE_ALLOCATED_AMOUNT = "invoiceAllocatedAmount";
    public static final String QUOTE_INFO = "quoteInfo";
    public static final String POLICY = "policy";
    public static final String POLICY_EFFECTIVE_DATE = "policyEffectiveDate";
    public static final String PRODUCER = "producer";
    public static final String LOCATION_ID = "locationId";
    public static final String AGENCY_NAME = "agencyName";
    public static final String ADDRESS = "address";
    public static final String ZIP = "zip";
    public static final String COMMISSION_PERCENTAGE = "commissionPercentage";
    public static final String UNITED_STATES_ABBR = "US";
}
