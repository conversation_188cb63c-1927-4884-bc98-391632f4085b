package com.concirrus.reporting.constant;

public class ReportingConstant {
    // Email constants
    public static final String WHITESPACE_AVIATION_REPORT = "WHITESPACE_AVIATION_REPORT";
    public static final String WHITESPACE_AVIATION_FAILED_REPORT = "WHITESPACE_AVIATION_FAILED_REPORT";
    public static final String AVIATION_LOB = "AVIATION";
    public static final String RECEIVER_NAME = "receiverName";
    public static final String SUBMITTED_BY = "submittedBy";
    public static final String SUBMISSION_DATE = "submissionDate";
    public static final String TIME_TO_GENERATE_JSON = "timeToGenerateJson";
    public static final String TIME_TO_COMPLETE_PROCESSING = "timeToCompleteProcessing";
    public static final String INSURED_NAME = "insuredName";
    public static final String COMMENT = "comment";
    
    private ReportingConstant() {
        // Private constructor to prevent instantiation
    }
}