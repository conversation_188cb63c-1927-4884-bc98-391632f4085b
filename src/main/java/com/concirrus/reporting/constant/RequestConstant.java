package com.concirrus.reporting.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RequestConstant {
    public static final String CLIENT_ID = "client-id";
    public static final String USER_ID = "x-user-id";
    public static final String USER_ROLES = "x-user-roles";

    public static final int PAGE_SIZE_MIN = 1;
    public static final int PAGE_SIZE_MAX = 100;

    public static final String CANNOT_BE_NULL = "Cannot be null or empty";
    public static final String LINE_OF_BUSINESS = "line-of-business";
}
