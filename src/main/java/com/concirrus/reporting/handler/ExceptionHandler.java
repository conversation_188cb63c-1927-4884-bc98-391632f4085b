package com.concirrus.reporting.handler;


import com.concirrus.reporting.model.exception.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.Date;

@Slf4j
@ControllerAdvice
@Order(1)
public class ExceptionHandler {

    public static final String EXCEPTION_MESSAGE = "EXCEPTION_MESSAGE - {}";


    @ResponseBody
    @org.springframework.web.bind.annotation.ExceptionHandler(NoDataFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ErrorMessage notFoundException(HttpServletRequest request, Exception exception) {
        ErrorMessage message = getErrorMessage(request, exception, HttpStatus.NOT_FOUND.value());
        log.error(EXCEPTION_MESSAGE, message);
        return message;
    }

    @ResponseBody
    @org.springframework.web.bind.annotation.ExceptionHandler({MissingServletRequestParameterException.class,
            MissingRequestHeaderException.class, BadRequestException.class, MethodArgumentNotValidException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ErrorMessage missingRequestParams(HttpServletRequest request, Exception exception) {
        ErrorMessage message = getErrorMessage(request, exception, HttpStatus.BAD_REQUEST.value());
        log.error(EXCEPTION_MESSAGE, message);
        return message;
    }

    @ResponseBody
    @org.springframework.web.bind.annotation.ExceptionHandler({InvalidArgumentsException.class, IllegalArgumentException.class, ConstraintViolationException.class})
    @ResponseStatus(HttpStatus.NOT_ACCEPTABLE)
    public ErrorMessage invalidArgumentsException(HttpServletRequest request, Exception exception) {
        ErrorMessage message = getErrorMessage(request, exception, HttpStatus.NOT_ACCEPTABLE.value());
        log.error(EXCEPTION_MESSAGE, message);
        return message;
    }

    @ResponseBody
    @org.springframework.web.bind.annotation.ExceptionHandler({TooEarlyRequestException.class})
    @ResponseStatus(HttpStatus.TOO_EARLY)
    public ErrorMessage tooEarlyRequestException(HttpServletRequest request, Exception exception) {
        ErrorMessage message = getErrorMessage(request, exception, HttpStatus.TOO_EARLY.value());
        log.error(EXCEPTION_MESSAGE, message);
        return message;
    }

    @ResponseBody
    @org.springframework.web.bind.annotation.ExceptionHandler(NotImplementedException.class)
    @ResponseStatus(HttpStatus.NOT_IMPLEMENTED)
    public ErrorMessage notImplementedException(HttpServletRequest request, Exception exception) {
        ErrorMessage message = getErrorMessage(request, exception, HttpStatus.NOT_IMPLEMENTED.value());
        log.error(EXCEPTION_MESSAGE, message);
        return message;
    }

    @ResponseBody
    @org.springframework.web.bind.annotation.ExceptionHandler(AlreadyExist.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public ErrorMessage alreadyExist(HttpServletRequest request, Exception exception) {
        ErrorMessage message = getErrorMessage(request, exception, HttpStatus.CONFLICT.value());
        log.error(EXCEPTION_MESSAGE, message);
        return message;
    }

    /**
     * Exception handling for wrong Date input with upgraded Jackson version 2.9.2
     * Added for lenient=OptBoolean.FALSE option in policy-info.
     * Handling of invalid date at controller itself. for example : 29th FEB 2018 or 31st APR 2018.
     *
     * @param request
     * @param exception
     * @return
     */
    @ResponseBody
    @org.springframework.web.bind.annotation.ExceptionHandler(org.springframework.http.converter.HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public ErrorMessage httpMessageNotReadableException(HttpServletRequest request, Exception exception) {
        ErrorMessage message = getErrorMessage(request, exception, HttpStatus.UNPROCESSABLE_ENTITY.value());

        log.error(EXCEPTION_MESSAGE, message);
        return message;
    }

    /**
     * Similar to final catch block
     *
     * @param request
     * @param exception
     * @return
     */
    @ResponseBody
    @org.springframework.web.bind.annotation.ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ErrorMessage internalServerResponse(HttpServletRequest request, Exception exception) {
        ErrorMessage message = getErrorMessage(request, exception, HttpStatus.INTERNAL_SERVER_ERROR.value());
        log.error(EXCEPTION_MESSAGE, message);
        return message;
    }

    private ErrorMessage getErrorMessage(HttpServletRequest request, Exception exception, int status) {
        log.error(EXCEPTION_MESSAGE, ExceptionUtils.getStackTrace(exception));
        ErrorMessage message = new ErrorMessage();
        message.setTimestamp(new Date().getTime());
        message.setError(exception.getMessage());
        message.setMessage(exception.getMessage());
        message.setStatus(status);
        message.setException(exception.getClass().getSimpleName());
        message.setPath(request.getRequestURI());
        return message;
    }
}
