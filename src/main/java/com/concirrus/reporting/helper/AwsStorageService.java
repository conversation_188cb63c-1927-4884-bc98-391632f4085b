package com.concirrus.reporting.helper;

import com.concirrus.reporting.model.exception.BadRequestException;
import com.concirrus.reporting.model.exception.InvalidArgumentsException;
import com.concirrus.reporting.utils.LoggingUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

import java.io.InputStream;
import java.net.URL;
import java.nio.ByteBuffer;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

@Component
@ConditionalOnProperty(value = "cloud.provider", havingValue = "aws")
public class AwsStorageService implements StorageService {
    private static final Logger logger = LoggerFactory.getLogger(AwsStorageService.class);

    private final S3Client s3Client;
    private final S3Presigner s3Presigner;

    public AwsStorageService(
            @Value("${cloud.aws.accessKeyId}") String awsAccessKey,
            @Value("${cloud.aws.secretKey}") String awsSecretKey,
            @Value("${cloud.aws.region}") String awsRegion) {
        this.s3Client = S3Client.builder()
                .region(Region.of(awsRegion))
                .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(awsAccessKey, awsSecretKey)))
                .build();
        this.s3Presigner = S3Presigner.builder()
                .region(Region.of(awsRegion))
                .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(awsAccessKey, awsSecretKey)))
                .build();
    }

    @Override
    public String storeObject(String bucketName, String objectName, byte[] objectData) {
        logger.info(LoggingUtils.logMethodEntry(bucketName, objectName));
        PutObjectRequest request = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(objectName)
                .build();
        s3Client.putObject(request, RequestBody.fromByteBuffer(ByteBuffer.wrap(objectData)));
        logger.info(LoggingUtils.logMethodExit());
        return objectName;
    }

    @Override
    public void storeObject(String bucketName, String fileName, InputStream inputStream, long contentLength) {
        logger.info(LoggingUtils.logMethodEntry(bucketName, fileName));

        PutObjectRequest request = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(fileName)
                .build();

        try {
            s3Client.putObject(request, RequestBody.fromInputStream(inputStream, contentLength));
        } catch (Exception e) {
            logger.error("Error uploading file to S3: ", e);
            throw new RuntimeException("Error uploading file to S3", e);
        }

        logger.info(LoggingUtils.logMethodExit());
    }


    @Override
    public void deleteObject(String bucketName, String objectName) {
        logger.info(LoggingUtils.logMethodEntry(bucketName, objectName));
        DeleteObjectRequest request = DeleteObjectRequest.builder()
                .bucket(bucketName)
                .key(objectName)
                .build();
        s3Client.deleteObject(request);
        logger.info(LoggingUtils.logMethodExit());
    }

    @Override
    public void deleteMultipleObjects(String bucketName, List<String> objectNames) {
        logger.info(LoggingUtils.logMethodEntry(bucketName, objectNames));
        if (CollectionUtils.isEmpty(objectNames)) {
            String errorMessage = "ObjectKeys cannot be null or empty";
            logger.error(errorMessage);
            throw new BadRequestException(errorMessage);
        }
        List<ObjectIdentifier> objects = new ArrayList<>();
        for (String objectName : objectNames) {
            objects.add(ObjectIdentifier.builder().key(objectName).build());
        }
        DeleteObjectsRequest request = DeleteObjectsRequest.builder()
                .bucket(bucketName)
                .delete(Delete.builder().objects(objects).build())
                .build();
        s3Client.deleteObjects(request);
        logger.info(LoggingUtils.logMethodExit());
    }

    @Override
    public String generatePreSignedUrl(String bucketName, String objectKey, int expirationDays) {
        logger.info(LoggingUtils.logMethodEntry(bucketName, objectKey, expirationDays));
        if (expirationDays > 7) {
            logger.error("Expiration days cannot exceed the upper limit : {}", 7);
            throw new InvalidArgumentsException("Expiration days cannot exceed the upper limit : " + 7);
        }
        GetObjectRequest request = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(objectKey)
                .build();
        GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(Duration.ofDays(expirationDays))
                .getObjectRequest(request)
                .build();
        PresignedGetObjectRequest presignedGetObjectRequest = s3Presigner.presignGetObject(presignRequest);
        URL url = presignedGetObjectRequest.url();
        logger.info(LoggingUtils.logMethodExit());
        return url.toString();
    }
}
