package com.concirrus.reporting.helper;

import java.io.InputStream;
import java.util.List;

public interface StorageService {
    String storeObject(String bucketName, String objectName, byte[] objectData);
    void storeObject(String bucketName, String fileName, InputStream inputStream, long contentLength);
    void deleteObject(String bucketName, String objectName);
    void deleteMultipleObjects(String bucketName, List<String> objectNames);
    String generatePreSignedUrl(String bucketName, String objectKey, int expirationDays);
}
