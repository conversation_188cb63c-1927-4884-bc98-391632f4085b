package com.concirrus.reporting.helper;

import com.concirrus.reporting.model.exception.BadRequestException;
import com.concirrus.reporting.utils.LoggingUtils;
import com.google.cloud.WriteChannel;
import com.google.cloud.storage.*;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.BufferedInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.nio.channels.Channels;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@ConditionalOnProperty(value = "cloud.provider", havingValue = "gcp")
public class GcpStorageService implements StorageService {
    private static final Logger logger = LoggerFactory.getLogger(GcpStorageService.class);

    private final Storage storage;

    public GcpStorageService(@Value("${cloud.gcp.project-id}") String projectId) {
        this.storage = StorageOptions.newBuilder()
                .setProjectId(projectId)
                .build()
                .getService();;
    }

    @Override
    public String storeObject(String bucketName, String objectName, byte[] objectData) {
        logger.info(LoggingUtils.logMethodEntry(bucketName, objectName));
        BlobId blobId = BlobId.of(bucketName, objectName);
        BlobInfo blobInfo = BlobInfo.newBuilder(blobId).build();

        Blob blob = storage.create(blobInfo, objectData);
        logger.info(LoggingUtils.logMethodExit());
        return blob.getCrc32c();
    }

    @Override
    public void storeObject(String bucketName, String fileName, InputStream inputStream, long contentLength) {
        logger.info("Uploading file to bucket: {}, with name: {}", bucketName, fileName);

        BlobId blobId = BlobId.of(bucketName, fileName);
        BlobInfo blobInfo = BlobInfo.newBuilder(blobId).build();

        try (
                WriteChannel writer = storage.writer(blobInfo);
                OutputStream outputStream = Channels.newOutputStream(writer);
                BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream)
        ) {
            int chunkSize = 2 * 1024 * 1024; // 2MB
            writer.setChunkSize(chunkSize);

            byte[] buffer = new byte[chunkSize];
            int bytesRead;
            while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            outputStream.flush();
            logger.info("File uploaded successfully to {}/{}", bucketName, fileName);
        } catch (Exception e) {
            logger.error("Error uploading file to GCS: ", e);
            throw new RuntimeException("Failed to upload file", e);
        }
    }

    @Override
    public void deleteObject(String bucketName, String objectName) {
        logger.info(LoggingUtils.logMethodEntry(bucketName, objectName));
        BlobId blobId = BlobId.of(bucketName, objectName);
        boolean deleted = storage.delete(blobId);
        if (deleted) {
            logger.info("Object {} deleted from bucket {}", objectName, bucketName);
        } else {
            logger.error("Failed to delete object {} from bucket {}", objectName, bucketName);
        }
        logger.info(LoggingUtils.logMethodExit());
    }

    @Override
    public void deleteMultipleObjects(String bucketName, List<String> objectNames) {
        logger.info(LoggingUtils.logMethodEntry(bucketName, objectNames));
        if (CollectionUtils.isEmpty(objectNames)) {
            String errorMessage = "ObjectKeys cannot be null or empty";
            logger.error(errorMessage);
            throw new BadRequestException(errorMessage);
        }
        for (String objectName : objectNames) {
            deleteObject(bucketName, objectName);
        }
        logger.info(LoggingUtils.logMethodExit());
    }

    @Override
    public String generatePreSignedUrl(String bucketName, String objectName, int expirationDays) {
        logger.info(LoggingUtils.logMethodEntry(bucketName, objectName, expirationDays));
        BlobInfo blobInfo = BlobInfo.newBuilder(BlobId.of(bucketName, objectName)).build();
        URL url = storage.signUrl(blobInfo, expirationDays, TimeUnit.DAYS, Storage.SignUrlOption.withV4Signature());
        logger.info(LoggingUtils.logMethodExit());
        return url.toString();
    }
}