package com.concirrus.reporting.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;

@Configuration
@Slf4j
public class CloudConfig {

    @Bean
    public String bucketName(
            @Value("${cloud.provider}") String cloudProvider,
            @Value("${cloud.bucket.name.aws}") String awsBucketName,
            @Value("${cloud.bucket.name.gcp}") String gcpBucketName
    ) {
        return "aws".equals(cloudProvider) ? awsBucketName : gcpBucketName;
    }
}
