package com.concirrus.reporting.config;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;


/**
 * Mongo Config for policy DB
 *
 * <AUTHOR>
 * @since Jan-2024
 */
@Configuration
public class MongoConfig {

    private static final Logger LOG = LoggerFactory.getLogger(MongoConfig.class);
    public static final String REPORTING_MONGO_TEMPLATE = "reportingInfoMongoTemplate";
    public static final String POLICY_INFO_MONGO_TEMPLATE = "policyInfoMongoTemplatePrimary";

    public static final String AUDIT_MONGO_TEMPLATE = "auditInfoMongoTemplate";

    @Value("${mongo.policy.db}")
    private String policyDB;

    @Value("${mongo.reporting.db}")
    private String reportingDB;

    @Value("${mongo.audit.db}")
    private String auditDB;

    @Value("${mongo.policy.uri}")
    private String uri;

    @Primary
    @Bean(name = REPORTING_MONGO_TEMPLATE)
    public MongoTemplate getReportingMongoTemplate() {
        LOG.info("Creating mongoTemplate by URI :{}", uri);
        return new MongoTemplate(getMongoFactory(uri, reportingDB));
    }

    @Bean(name = POLICY_INFO_MONGO_TEMPLATE)
    public MongoTemplate getPolicyInfoMongoTemplate() {
        LOG.info("Creating mongoTemplate by URI :{}", uri);
        return new MongoTemplate(getMongoFactory(uri, policyDB));
    }

    @Bean(name = AUDIT_MONGO_TEMPLATE)
    public MongoTemplate getAuditInfoMongoTemplate() {
        LOG.info("Creating mongoTemplate by URI :{}", uri);
        return new MongoTemplate(getMongoFactory(uri, auditDB));
    }

    /**
     * Creates a MongoDB database factory based on the provided MongoDB connection URI and database name.
     *
     * @param uri    The connection URI for MongoDB.
     * @param dbName The name of the MongoDB database.
     * @return A MongoDatabaseFactory instance based on the provided parameters.
     */
    public static MongoDatabaseFactory getMongoFactory(String uri, String dbName) {
        MongoClient mongoClient = MongoClients.create(uri);
        MongoDatabaseFactory factory = new SimpleMongoClientDatabaseFactory(mongoClient, dbName);
        LOG.info("Successfully created SIMPLE_MONGO_FACTORY with \n dbName : {}, \n mongoClient :{}",
                dbName, mongoClient);
        return factory;
    }
}
