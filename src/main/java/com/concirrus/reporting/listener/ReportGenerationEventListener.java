package com.concirrus.reporting.listener;

import com.concirrus.reporting.event.ReportGenerationEvent;
import com.concirrus.reporting.service.ReportingService;
import com.concirrus.reporting.utils.LoggingUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ReportGenerationEventListener implements ApplicationListener<ReportGenerationEvent> {
    private final ReportingService reportingService;
    @Autowired
    public ReportGenerationEventListener(ReportingService reportingService) {
        this.reportingService = reportingService;
    }

    /**
     * Asynchronous event listener method triggered when a ReportGenerationEvent is published.
     * Initiates the report generation process based on the event details.
     *
     * @param event The ReportGenerationEvent containing information about the report generation.
     */
    @Override
    @Async
    public void onApplicationEvent(ReportGenerationEvent event) {
        log.info(LoggingUtils.logMethodEntry(event.getReportingType()));
        reportingService.reportGeneration(event.getStartDate(), event.getEndDate(), event.getReportingType(), event.getClientId(), event.getReportRequestInfo(), "APPLIED_CONSTRUCTION");
        log.info(LoggingUtils.logMethodExit());
    }
}
