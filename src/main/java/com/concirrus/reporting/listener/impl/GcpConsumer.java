package com.concirrus.reporting.listener.impl;

import com.concirrus.reporting.dto.AlertDTO;
import com.concirrus.reporting.listener.EventConsumer;
import com.concirrus.reporting.service.AlertService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.cloud.spring.pubsub.core.PubSubTemplate;
import com.google.cloud.spring.pubsub.integration.AckMode;
import com.google.cloud.spring.pubsub.integration.inbound.PubSubInboundChannelAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;

import static com.concirrus.reporting.constant.CommonConstant.*;

@Service
@ConditionalOnProperty(value = "cloud.provider", havingValue = "gcp")
public class GcpConsumer {
    private static final Logger logger = LoggerFactory.getLogger(GcpConsumer.class);

    private final AlertService alertService;
    private final String slackAlertChannelUrl;
    private final String serviceContext;
    private final EventConsumer eventConsumer;

    @Autowired
    public GcpConsumer(AlertService alertService,
                       @Value("${queue.url.out.slack-alert}") String slackAlertChannelUrl,
                       @Value("${server.servlet.context-path}") String serviceContext,
                       EventConsumer eventConsumer) {
        this.alertService = alertService;
        this.slackAlertChannelUrl = slackAlertChannelUrl;
        this.serviceContext = serviceContext;
        this.eventConsumer = eventConsumer;
    }

    @Bean
    public DirectChannel submissionExtractConsumerChannel() {
        return new DirectChannel();
    }

    @Bean
    public PubSubInboundChannelAdapter submissionExtractConsumerChannelAdapter(
            MessageChannel submissionExtractConsumerChannel, 
            PubSubTemplate pubSubTemplate, 
            @Value("${cloud.queue.in.consumer-reporting}") String consumerQueue) {
        PubSubInboundChannelAdapter adapter = new PubSubInboundChannelAdapter(pubSubTemplate, consumerQueue);
        adapter.setOutputChannel(submissionExtractConsumerChannel);
        adapter.setAckMode(AckMode.AUTO);
        return adapter;
    }

    @ServiceActivator(inputChannel = "submissionExtractConsumerChannel")
    private void processMessage(Message<String> message) {
        logger.info("Received message in reporting service: {}", message.getPayload());
        String clientId = null;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(message.getPayload());
            clientId = rootNode.path("clientId").asText(null);
            eventConsumer.processMessage(message.getPayload());
            logger.info("Successfully processed message for clientId: {}", clientId);
        } catch (Exception e) {
            logger.error("Error processing message for clientId: {}, Error: {}", clientId, e.getMessage(), e);
            String alertMessage = LEFT_SQUARE_BRACKET + serviceContext + RIGHT_SQUARE_BRACKET +
                    CLIENT_ID_BOLD + clientId + ERROR_BOLD +
                    e.getMessage() + WRONG_SYMBOL;

            try {
                alertService.postAlert(slackAlertChannelUrl, new AlertDTO(alertMessage));
                logger.info("Alert sent to Slack for failed message processing");
            } catch (Exception alertException) {
                logger.error("Failed to send alert to Slack: {}", alertException.getMessage(), alertException);
            }
            logger.info("Message processing failed but will be acknowledged to prevent redelivery");
        }
    }
}