package com.concirrus.reporting.listener;

import com.concirrus.reporting.event.SubmissionExtractionEvent;
import com.concirrus.reporting.service.ReportingService;
import com.concirrus.reporting.utils.LoggingUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SubmissionExtractionEventListener implements ApplicationListener<SubmissionExtractionEvent> {
    private final ReportingService reportingService;
    
    @Autowired
    public SubmissionExtractionEventListener(ReportingService reportingService) {
        this.reportingService = reportingService;
    }

    /**
     * Asynchronous event listener method triggered when a SubmissionExtractionEvent is published.
     * Initiates the submission extract report generation process based on the event details.
     *
     * @param event The SubmissionExtractionEvent containing information about the submission extraction.
     */
    @Override
    @Async
    public void onApplicationEvent(SubmissionExtractionEvent event) {
        log.info(LoggingUtils.logMethodEntry(event.getSubmissionRequestDTO().getSubmissionId()));
        reportingService.generateSubmissionExtractReport(
            event.getSubmissionRequestDTO(), 
            event.getClientId(), 
            event.getReportRequestInfo()
        );
        log.info(LoggingUtils.logMethodExit());
    }
}