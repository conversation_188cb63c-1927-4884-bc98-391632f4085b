package com.concirrus.reporting.listener;

import com.concirrus.reporting.dto.ReportRequestInfoDTO;
import com.concirrus.reporting.dto.SubmissionRequestDTO;
import com.concirrus.reporting.service.ReportingService;
import com.concirrus.reporting.utils.LoggingUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;

@Service
@Slf4j
@RequiredArgsConstructor
public class EventConsumer {
    private final ReportingService reportingService;
    private final ObjectMapper objectMapper;

    public void processMessage(String message) throws JsonProcessingException {
        Instant start = Instant.now();
        log.info("Processing submission extract message: {}", message);

        try {
            // Directly deserialize the entire payload to SubmissionRequestDTO
            SubmissionRequestDTO submissionRequestDTO = objectMapper.readValue(message, SubmissionRequestDTO.class);
            
            String submissionId = submissionRequestDTO.getSubmissionId();
            String clientId = submissionRequestDTO.getClientId();

            if (submissionId == null || submissionId.isEmpty()) {
                log.error("submissionId field not found in message");
                throw new IllegalArgumentException("Missing submissionId field in message");
            }

            if (submissionRequestDTO.getSubmissionBody() == null) {
                log.error("submissionBody field not found in message");
                throw new IllegalArgumentException("Missing submissionBody field in message");
            }

            LoggingUtils.logMethodEntry(submissionId, clientId);

            String result = reportingService.createSubmissionExtractReport(
                submissionRequestDTO, clientId
            );
            
            log.info("Successfully processed submission extract report via Pub/Sub: {}", result);
            LoggingUtils.logMethodExitWithMemoryAndDuration(start);
            
        } catch (Exception e) {
            log.error("Error processing message: {}", e.getMessage(), e);
            LoggingUtils.logMethodExitWithMemoryAndDuration(start);
            throw e;
        }
    }
}