package com.concirrus.reporting.dal;

import com.concirrus.reporting.enums.ReportingType;
import org.bson.Document;
import org.springframework.data.domain.PageRequest;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface EndorsementDAL {

    List<Document> getPolicyEndorsementsBy(LocalDate startDate, LocalDate endDate, List<String> statuses,
                                           Boolean isInvoiceRequired,
                                           Boolean isSequenceRequired,
                                           String clientId, PageRequest pageRequest);

    //List<Document> getPolicyEndorsementsByPolicyIdsStatusesAndClient(Collection<String> policyIds, List<String> statuses, String clientId, boolean isExcludePreviousPolicyInfo);
    Map<String, List<Document>> getSortedEndorsementsGroupedByPolicy(Collection<String> policyIds, List<String> statuses, String clientId, boolean isExcludePreviousPolicyInfo, ReportingType reportingType);

    List<Document> getInvoicedPolicyEndorsementsByPolicyIdsAndClient(Collection<String> policyIds, String clientId);
}
