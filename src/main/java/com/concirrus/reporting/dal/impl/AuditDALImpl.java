package com.concirrus.reporting.dal.impl;

import com.concirrus.reporting.dal.AuditDAL;
import com.concirrus.reporting.dal.PolicyDAL;
import com.concirrus.reporting.utils.LoggingUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

import static com.concirrus.reporting.config.MongoConfig.AUDIT_MONGO_TEMPLATE;
import static com.concirrus.reporting.config.MongoConfig.POLICY_INFO_MONGO_TEMPLATE;
import static com.concirrus.reporting.constant.DBConstant.*;
import static com.concirrus.reporting.utils.QueryUtils.getPaginationQueryForMongo;


@Slf4j
@Repository
public class AuditDALImpl implements AuditDAL {

    private final MongoTemplate auditInfoMongoTemplate;

    @Autowired
    public AuditDALImpl(@Qualifier(AUDIT_MONGO_TEMPLATE) MongoTemplate auditInfoMongoTemplate) {
        this.auditInfoMongoTemplate = auditInfoMongoTemplate;
    }

    @Override
    public Document getPoliciesBy(String policyId, String state, String clientId, PageRequest pageRequest) {
        log.info(LoggingUtils.logMethodEntry(policyId, state, clientId, pageRequest));
        Query query = new Query();

        if (Objects.nonNull(pageRequest)) {
            query = getPaginationQueryForMongo(query, pageRequest);
        }

        if (StringUtils.isNotEmpty(clientId)) {
            query.addCriteria(Criteria.where(PAYLOAD_CLIENT_ID).is(clientId));
        }

        if (StringUtils.isNotEmpty(policyId)) {
            query.addCriteria(Criteria.where(PAYLOAD_POLICY_ID).is(policyId));
        }

        if (StringUtils.isNotEmpty(state)) {
            query.addCriteria(Criteria.where(PAYLOAD_STATE).is(state));
        }

        // IS_DELETED criteria
        Criteria isDeletedCriteria = new Criteria().orOperator(
                Criteria.where(PAYLOAD_IS_DELETED).is(false),
                Criteria.where(PAYLOAD_IS_DELETED).exists(false)
        );

        query.addCriteria(isDeletedCriteria);

        log.info("policyId: {}, state: {}, clientId: {}", policyId, state, clientId);

        log.info("Generated Query: {}", query);

        // Fetch the latest record
        Document auditPolicyBoundDocument = auditInfoMongoTemplate.findOne(query, Document.class, AUDIT_POLICY_COLLECTION);

        if(auditPolicyBoundDocument != null) {
            log.info("auditPolicyBoundDocument: {}", auditPolicyBoundDocument.toJson());
        }

        return auditPolicyBoundDocument;
    }

}
