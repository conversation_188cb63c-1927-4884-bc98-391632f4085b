package com.concirrus.reporting.dal.impl;

import com.concirrus.reporting.dal.ReportingDAL;
import com.concirrus.reporting.entity.ReportRequestInfo;
import com.concirrus.reporting.model.exception.BadRequestException;
import com.concirrus.reporting.model.exception.NoDataFoundException;
import com.concirrus.reporting.utils.LoggingUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

import static com.concirrus.reporting.config.MongoConfig.REPORTING_MONGO_TEMPLATE;
import static com.concirrus.reporting.constant.DBConstant.*;
import static com.concirrus.reporting.utils.QueryUtils.getPaginationQueryForMongo;

@Slf4j
@Repository
public class ReportingDALImpl implements ReportingDAL {

    private final MongoTemplate reportInfoMongoTemplate;

    @Autowired
    public ReportingDALImpl(@Qualifier(REPORTING_MONGO_TEMPLATE) MongoTemplate reportInfoMongoTemplate) {
        this.reportInfoMongoTemplate = reportInfoMongoTemplate;
    }

    /**
     * Adds a new report request to the data store.
     *
     * @param reportRequestInfo The ReportRequestInfo object to be added.
     * @return The added ReportRequestInfo object.
     */
    @Override
    public ReportRequestInfo addReportRequest(ReportRequestInfo reportRequestInfo) {
        log.info(LoggingUtils.logMethodEntry(reportRequestInfo));
        return reportInfoMongoTemplate.save(reportRequestInfo);
    }

    /**
     * Updates an existing report request in the data store.
     *
     * @param requestId               The ID of the report request to be updated.
     * @param clientId                The client ID associated with the report request.
     * @param updateReportRequestInfo The updated ReportRequestInfo object.
     * @return The updated ReportRequestInfo object.
     */
    @Override
    public ReportRequestInfo updateReportRequest(String requestId, String clientId, ReportRequestInfo updateReportRequestInfo) {
        log.info(LoggingUtils.logMethodEntry(requestId, clientId));
        Query query = new Query();

        query.addCriteria(Criteria.where(MONGO_ID).is(requestId));

        if (StringUtils.isNotEmpty(clientId)) {
            query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
        }

        // Replace the document with the updated one
        reportInfoMongoTemplate.findAndReplace(query, updateReportRequestInfo);
        return updateReportRequestInfo;
    }

    /**
     * Retrieves a specific report request by its ID and client ID from the data store.
     *
     * @param requestId The ID of the report request to retrieve.
     * @param clientId  The client ID associated with the report request.
     * @return The ReportRequestInfo object corresponding to the specified ID and client ID.
     * @throws NoDataFoundException If no report request is found for the given ID and client ID.
     */
    @Override
    public ReportRequestInfo getReportRequestById(String requestId, String clientId) {
        log.info(LoggingUtils.logMethodEntry(requestId, clientId));
        Query query = new Query();
        query.addCriteria(Criteria.where(MONGO_ID).is(requestId));

        if (StringUtils.isNotEmpty(clientId)) {
            query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
        }

        ReportRequestInfo request = reportInfoMongoTemplate.findOne(query, ReportRequestInfo.class);

        if (Objects.isNull(request)) {
            log.error("No report request exist for ID : {}", requestId);
            throw new NoDataFoundException("No report request exist for ID : " + requestId);
        }

        return request;
    }

    /**
     * Retrieves a pair containing the total count of {@code ReportRequestInfo} records
     * that match the specified criteria and a paginated list of matching records.
     *
     * @param pageRequest The pagination information for fetching a subset of records.
     * @param clientId    The client ID for filtering records. Can be {@code null} or empty.
     * @param date        The minimum creation date for filtering records. Can be {@code null}.
     * @return A {@code Pair} containing the total count of matching records and the paginated list of records.
     * @see org.springframework.data.util.Pair
     */
    @Override
    public Pair<Long, List<ReportRequestInfo>> getPairOfTotalCountAndReportRequestInfoList(PageRequest pageRequest, String clientId, LocalDate date) {
        log.info(LoggingUtils.logMethodEntry(pageRequest, clientId));

        Query query = new Query();

        if (StringUtils.isNotEmpty(clientId)) {
            query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
        }

        if (Objects.nonNull(date)) {
            query.addCriteria(Criteria.where(CREATED_AT).gte(date));
        }

        // total count of report that matches the condition
        long totalCount = reportInfoMongoTemplate.count(query, ReportRequestInfo.class);

        // paginated Query
        query = getPaginationQueryForMongo(query, pageRequest);
        List<ReportRequestInfo> paginatedData = reportInfoMongoTemplate.find(query, ReportRequestInfo.class);

        return Pair.of(totalCount, paginatedData);
    }

    /**
     * Retrieves a list of report request information from the MongoDB collection that were created before the specified date,
     * optionally filtered by client ID and paginated using the provided page request.
     *
     * @param pageRequest The pagination information to apply to the query. Can be {@code null} if pagination is not needed.
     * @param clientId    The client ID to filter the report request information. Can be {@code null} or empty to include all clients.
     * @param date        The date before which the report request information was created. Can be {@code null} to include all dates.
     * @return A list of report request information objects that meet the specified criteria.
     */
    @Override
    public List<ReportRequestInfo> getReportRequestInfoListBeforeDate(PageRequest pageRequest, String clientId, LocalDate date) {
        log.info(LoggingUtils.logMethodEntry(pageRequest, clientId, date));
        Query query = new Query();

        if (StringUtils.isNotEmpty(clientId)) {
            query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
        }

        if (Objects.nonNull(date)) {
            query.addCriteria(Criteria.where(CREATED_AT).lt(date));
        }

        if (Objects.nonNull(pageRequest)) {
            // paginated Query
            query = getPaginationQueryForMongo(query, pageRequest);
        }

        return reportInfoMongoTemplate.find(query, ReportRequestInfo.class);
    }

    /**
     * Deletes report request information from the MongoDB collection based on the provided list of IDs.
     * Throws a {@link BadRequestException} if the list of IDs is null or empty.
     *
     * @param ids A list of String IDs representing the report request information to be deleted.
     * @throws BadRequestException if the list of IDs is null or empty.
     */
    @Override
    public void deleteReportRequestInfoByIds(List<String> ids) {
        log.info(LoggingUtils.logMethodEntry(ids));

        if (CollectionUtils.isEmpty(ids)) {
            String errorMessage = "Reporting request ids cannot be null or empty";
            log.error(errorMessage);
            throw new BadRequestException(errorMessage);
        }

        Query query = new Query();
        query.addCriteria(Criteria.where(MONGO_ID).in(ids));

        reportInfoMongoTemplate.remove(query, ReportRequestInfo.class);
        log.info(LoggingUtils.logMethodExit());
    }
}
