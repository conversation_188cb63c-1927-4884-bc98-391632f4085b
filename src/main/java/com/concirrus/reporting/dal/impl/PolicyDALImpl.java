package com.concirrus.reporting.dal.impl;

import com.concirrus.reporting.dal.PolicyDAL;
import com.concirrus.reporting.utils.LoggingUtils;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

import static com.concirrus.reporting.config.MongoConfig.POLICY_INFO_MONGO_TEMPLATE;
import static com.concirrus.reporting.constant.DBConstant.*;
import static com.concirrus.reporting.constant.ReportConstant.DOT;
import static com.concirrus.reporting.utils.QueryUtils.getPaginationQueryForMongo;


@Slf4j
@Repository
public class PolicyDALImpl implements PolicyDAL {

    private final MongoTemplate policyInfoMongoTemplate;

    @Autowired
    public PolicyDALImpl(@Qualifier(POLICY_INFO_MONGO_TEMPLATE) MongoTemplate policyInfoMongoTemplate) {
        this.policyInfoMongoTemplate = policyInfoMongoTemplate;
    }

    /**
     * Retrieves a list of policy documents from the MongoDB collection based on the specified filters.
     *
     * <p>This method allows searching for policies based on various criteria including start and end dates,
     * whether the premium has been paid, and the client ID. It also supports pagination of results.
     * Additionally, it ensures that deleted policies are excluded from the results.
     *
     * @param startDate     The start date to filter policies. Only policies with a start date greater than
     *                      or equal to this value will be returned. Can be null to ignore this filter.
     * @param endDate       The end date to filter policies. Only policies with an end date less than
     *                      or equal to this value will be returned. Can be null to ignore this filter.
     * @param isPremiumPaid A Boolean indicating whether to filter policies based on the premium payment status.
     *                      If true, only policies where the premium has been paid will be returned. If false,
     *                      policies where the premium has not been paid or where the field does not exist will
     *                      be returned. Can be null to ignore this filter.
     * @param clientId      The ID of the client to filter policies. Only policies belonging to the specified
     *                      client will be returned. Can be null to ignore this filter.
     * @param pageRequest   The pagination information for the query, including page number and size.
     *                      Can be null to return all matching policies without pagination.
     * @return A list of {@link Document} objects representing the policies that match the specified criteria.
     *         The list will be empty if no matching policies are found.
     * @throws org.springframework.data.mongodb.InvalidMongoDbApiUsageException If there is an error with the
     *         MongoDB query, such as including multiple 'null' criteria in an unsupported manner.
     */
    @Override
    public List<Document> getPoliciesBy(LocalDate startDate, LocalDate endDate, Boolean isPremiumPaid, String clientId, PageRequest pageRequest) {
        log.info(LoggingUtils.logMethodEntry(startDate, endDate, isPremiumPaid, clientId, pageRequest));
        Query query = new Query();

        if (Objects.nonNull(pageRequest)) {
            query = getPaginationQueryForMongo(query, pageRequest);
        }

        if (Objects.nonNull(clientId)) {
            query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
        }

        if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
            query.addCriteria(Criteria.where(POLICY_START_DATE).gte(startDate.toString()).lte(endDate.toString()));
        } else if (Objects.nonNull(startDate)) {
            query.addCriteria(Criteria.where(POLICY_START_DATE).gte(startDate.toString()));
        }

        Criteria premiumPaidMatchingCriteria = null;
        if (Objects.nonNull(isPremiumPaid)) {
            premiumPaidMatchingCriteria = new Criteria().orOperator(
                    Criteria.where(IS_PREMIUM_PAID).exists(false),
                    Criteria.where(IS_PREMIUM_PAID).is(isPremiumPaid)
            );
        }

        // IS_DELETED criteria
        Criteria isDeletedCriteria = new Criteria().orOperator(
                Criteria.where(IS_DELETED).is(false),
                Criteria.where(IS_DELETED).exists(false)
        );

        /*
            We encountered an error when using multiple OR operators in a query:
            org.springframework.data.mongodb.InvalidMongoDbApiUsageException: Due to limitations in com.mongodb.BasicDocument, you cannot include multiple 'null' criteria.

            To address this, we create separate criteria for each OR condition and combine them using an AND operator.
            This approach avoids the error by ensuring that only valid criteria are applied and respects the limitations of the underlying MongoDB API.
        */
        if(Objects.isNull(premiumPaidMatchingCriteria)){
            query.addCriteria(isDeletedCriteria);
        }else {
            query.addCriteria(new Criteria().andOperator(premiumPaidMatchingCriteria, isDeletedCriteria));
        }

        return policyInfoMongoTemplate.find(query, Document.class, INSURANCE_POLICY_COLLECTION);
    }

    @Override
    public List<Document> getPoliciesByInvoiceDate(LocalDate startDate, LocalDate endDate, String clientId, PageRequest pageRequest) {
        log.info(LoggingUtils.logMethodEntry(startDate, endDate, clientId, pageRequest));
        Query query = new Query();

        if (Objects.nonNull(pageRequest)) {
            query = getPaginationQueryForMongo(query, pageRequest);
        }

        if (Objects.nonNull(clientId)) {
            query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
        }

        if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
            query.addCriteria(Criteria.where(INVOICE_BILLED_DATE).gte(startDate.toString()).lte(endDate.toString()));
        }

        // IS_DELETED criteria
        query.addCriteria(Criteria.where(IS_DELETED).ne(true));

        return policyInfoMongoTemplate.find(query, Document.class, INSURANCE_POLICY_COLLECTION);
    }

    @Override
    public List<Document> getPoliciesByEffectiveDate(LocalDate startDate, LocalDate endDate, String clientId, PageRequest pageRequest) {
        log.info(LoggingUtils.logMethodEntry(startDate, endDate, clientId, pageRequest));
        Query query = new Query();

        if (Objects.nonNull(pageRequest)) {
            query = getPaginationQueryForMongo(query, pageRequest);
        }

        if (Objects.nonNull(clientId)) {
            query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
        }

        if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
            query.addCriteria(Criteria.where(QUOTE_INFO+DOT+POLICY+DOT+POLICY_EFFECTIVE_DATE).gte(startDate.toString()).lte(endDate.toString()));
        }

        // IS_DELETED criteria
        query.addCriteria(Criteria.where(IS_DELETED).ne(true));

        return policyInfoMongoTemplate.find(query, Document.class, INSURANCE_POLICY_COLLECTION);
    }
}
