package com.concirrus.reporting.dal.impl;

import com.concirrus.reporting.dal.EndorsementDAL;
import com.concirrus.reporting.enums.ReportingType;
import com.concirrus.reporting.utils.LoggingUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.concirrus.reporting.config.MongoConfig.POLICY_INFO_MONGO_TEMPLATE;
import static com.concirrus.reporting.constant.DBConstant.*;
import static com.concirrus.reporting.utils.QueryUtils.getPaginationQueryForMongo;


@Slf4j
@Repository
public class EndorsementDALImpl implements EndorsementDAL {

    private final MongoTemplate policyInfoMongoTemplate;

    @Autowired
    public EndorsementDALImpl(@Qualifier(POLICY_INFO_MONGO_TEMPLATE) MongoTemplate policyInfoMongoTemplate) {
        this.policyInfoMongoTemplate = policyInfoMongoTemplate;
    }

    /**
     * Retrieves a list of documents representing policy endorsements based on the provided criteria.
     *
     * @param startDate          The start date for the endorsements to be retrieved.
     * @param endDate            The end date for the endorsements to be retrieved.
     * @param statuses           A list of statuses that the endorsements should match. Pass `null` or an empty list to include endorsements with any status.
     * @param isInvoiceRequired  A Boolean indicating whether the endorsements require an invoice. Pass `true` to filter for endorsements requiring an invoice, `false` to filter for endorsements not requiring an invoice, or `null` to include endorsements with any invoice requirement status.
     * @param isSequenceRequired A Boolean indicating whether the endorsements require a sequence. Pass `true` to filter for endorsements requiring a sequence, `false` to filter for endorsements not requiring a sequence, or `null` to include endorsements with any sequence requirement status.
     * @param clientId           The ID of the client whose endorsements are to be retrieved.
     * @param pageRequest        The pagination information for the query. Pass `null` if pagination is not required.
     * @return A list of Document objects representing the endorsements matching the provided criteria.
     */
    @Override
    public List<Document> getPolicyEndorsementsBy(LocalDate startDate, LocalDate endDate, List<String> statuses,
                                                  Boolean isInvoiceRequired,
                                                  Boolean isSequenceRequired,
                                                  String clientId, PageRequest pageRequest) {
        log.info(LoggingUtils.logMethodEntry(startDate, endDate, statuses, isInvoiceRequired, isSequenceRequired, clientId, pageRequest));
        Query query = new Query();

        if (Objects.nonNull(pageRequest)) {
            query = getPaginationQueryForMongo(query, pageRequest);
        }

        if (Objects.nonNull(clientId)) {
            query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
        }

        if (CollectionUtils.isNotEmpty(statuses)) {
            query.addCriteria(Criteria.where(ENDORSEMENT_STATUS).in(statuses));
        }

        if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
            query.addCriteria(Criteria.where(ENDORSEMENT_EFFECTIVE_DATE).gte(startDate).lte(endDate));
        }

        if (Objects.nonNull(isInvoiceRequired) && isInvoiceRequired) {
            query.addCriteria(Criteria.where(INVOICE_ID).exists(isInvoiceRequired));
        }

        if (Objects.nonNull(isSequenceRequired) && isSequenceRequired) {
            query.addCriteria(Criteria.where(ENDORSEMENT_SEQUENCE).exists(isSequenceRequired));
        }

        return policyInfoMongoTemplate.find(query, Document.class, POLICY_ENDORSEMENT_COLLECTION);
    }

    @Override
//    public List<Document> getPolicyEndorsementsByPolicyIdsStatusesAndClient(Collection<String> policyIds, List<String> statuses, String clientId, boolean isExcludePreviousPolicyInfo) {
//        log.info(LoggingUtils.logMethodEntry(policyIds, statuses,  clientId ));
//        Query query = new Query();
//
//        // Ensure that the ENDORSEMENT_TYPE field is present in the record
//        query.addCriteria(Criteria.where(ENDORSEMENT_TYPE).exists(true));
//
//        if (Objects.nonNull(clientId)) {
//            query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
//        }
//
//        if (CollectionUtils.isNotEmpty(policyIds)) {
//            query.addCriteria(Criteria.where(POLICY_ID).in(policyIds));
//        }
//
//        if (CollectionUtils.isNotEmpty(statuses)) {
//            query.addCriteria(Criteria.where(ENDORSEMENT_STATUS).in(statuses));
//        }
//
//        if(isExcludePreviousPolicyInfo) {
//            // These fields are not used in reporting, so they are removed from the response.
//            query.fields().exclude(PREVIOUS_POLICY_INFO);  // Add more fields to exclude as needed
//        }
//
//        return policyInfoMongoTemplate.find(query, Document.class, POLICY_ENDORSEMENT_COLLECTION);
//    }

    public Map<String, List<Document>> getSortedEndorsementsGroupedByPolicy(
            Collection<String> policyIds, List<String> statuses, String clientId, boolean isExcludePreviousPolicyInfo, ReportingType reportingType) {

        log.info(LoggingUtils.logMethodEntry(policyIds, statuses, clientId, reportingType));

        List<AggregationOperation> pipeline = new ArrayList<>();

        // Match criteria (filters)
//        pipeline.add(Aggregation.match(Criteria.where(ENDORSEMENT_TYPE).exists(true)));

        if (Objects.nonNull(clientId)) {
            pipeline.add(Aggregation.match(Criteria.where(MONGO_CLIENT_ID).is(clientId)));
        }

        if (CollectionUtils.isNotEmpty(policyIds)) {
            pipeline.add(Aggregation.match(Criteria.where(POLICY_ID).in(policyIds)));
        }

        if (CollectionUtils.isNotEmpty(statuses)) {
            pipeline.add(Aggregation.match(Criteria.where(ENDORSEMENT_STATUS).in(statuses)));
        }

        if (isExcludePreviousPolicyInfo) {
            pipeline.add(Aggregation.project().andExclude(PREVIOUS_POLICY_INFO));
        }

        // Sort documents by createdAt in ascending order
        pipeline.add(Aggregation.sort(Sort.by(Sort.Direction.ASC, CREATED_AT)));

        // Group by policyId and collect sorted endorsements into an array
        pipeline.add(Aggregation.group(POLICY_ID)
                .push("$$ROOT").as("endorsements"));

        Aggregation aggregation = Aggregation.newAggregation(pipeline);

        // Execute aggregation and get the results
        List<Document> results = policyInfoMongoTemplate.aggregate(aggregation, POLICY_ENDORSEMENT_COLLECTION, Document.class).getMappedResults();

        // Convert List<Document> to Map<String, List<Document>>
        Map<String, List<Document>> endorsementsByPolicy = results.stream()
                .collect(Collectors.toMap(
                        doc -> doc.getString("_id"), // policyId is stored as _id in aggregation results
                        doc -> (List<Document>) doc.get("endorsements")
                ));

        return endorsementsByPolicy;
    }

//    /**
//     * Retrieves policy endorsements with invoices, filtered by policy IDs and client ID.
//     * Unnecessary fields are excluded from the response.
//     *
//     * @param policyIds a collection of policy IDs to filter endorsements; ignored if empty
//     * @param clientId  the client ID to filter endorsements; ignored if null
//     * @return a list of {@link Document} objects representing filtered policy endorsements
//     *
//     * This method filters for endorsements that:
//     * - Have an associated invoice
//     * - Match the specified client ID, if provided
//     * - Match any of the specified policy IDs, if provided
//     *
//     * Certain fields, such as previousPolicyInfo, are excluded as they are not needed in the response.
//     */
//    @Override
//    public List<Document> getInvoicedPolicyEndorsementsByPolicyIdsAndClient(Collection<String> policyIds, String clientId) {
//        log.info(LoggingUtils.logMethodEntry(policyIds, clientId));
//        Query query = new Query();
//
//        // Ensure that the invoiceId field is present in the record
//        query.addCriteria(Criteria.where(INVOICE_ID).exists(true));
//
//        if (Objects.nonNull(clientId)) {
//            query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
//        }
//
//        if (CollectionUtils.isNotEmpty(policyIds)) {
//            query.addCriteria(Criteria.where(POLICY_ID).in(policyIds));
//        }
//
//        // These fields are not used in reporting, so they are removed from the response.
//        query.fields().exclude(PREVIOUS_POLICY_INFO);  // Add more fields to exclude as needed
//
//        List<Document> endorsements = policyInfoMongoTemplate.find(query, Document.class, POLICY_ENDORSEMENT_COLLECTION);
//
//        // Group endorsements by policyId
//        Map<String, List<Document>> groupedEndorsements = endorsements.stream()
//                .collect(Collectors.groupingBy(doc -> doc.getString(POLICY_ID)));
//
//        List<Document> filteredEndorsements = new ArrayList<>();
//
//        for (Map.Entry<String, List<Document>> entry : groupedEndorsements.entrySet()) {
//            List<Document> policyEndorsements = entry.getValue();
//
//            // Sort endorsements by createdAt in ascending order
//            policyEndorsements.sort(Comparator.comparing(doc -> doc.getDate(CREATED_AT)));
//
//            // Find the last rewrite endorsement
//            int lastRewriteIndex = -1;
//            for (int i = 0; i < policyEndorsements.size(); i++) {
//                if ("REWRITE".equalsIgnoreCase(policyEndorsements.get(i).getString(ENDORSEMENT_TYPE))) {
//                    lastRewriteIndex = i;
//                }
//            }
//
//            // Add all endorsements after the last rewrite, including the rewrite itself
//            if (lastRewriteIndex != -1) {
//                filteredEndorsements.addAll(policyEndorsements.subList(lastRewriteIndex, policyEndorsements.size()));
//            } else {
//                filteredEndorsements.addAll(policyEndorsements);
//            }
//        }
//
//        log.info(LoggingUtils.logMethodExit());
//        return filteredEndorsements;
//    }

    @Override
    public List<Document> getInvoicedPolicyEndorsementsByPolicyIdsAndClient(Collection<String> policyIds, String clientId) {
        log.info(LoggingUtils.logMethodEntry(policyIds, clientId));

        try {
            // Create the base query with invoice ID criteria
            Query query = new Query(Criteria.where(INVOICE_ID).exists(true));

            // Add client ID filter if present
            if (StringUtils.isNotBlank(clientId)) {
                query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
            }

            // Add policy IDs filter if not empty
            if (CollectionUtils.isNotEmpty(policyIds)) {
                query.addCriteria(Criteria.where(POLICY_ID).in(policyIds));
            }

            // Exclude unnecessary fields
            query.fields().exclude(PREVIOUS_POLICY_INFO);

            // Fetch all endorsements
            List<Document> endorsements = policyInfoMongoTemplate.find(query, Document.class, POLICY_ENDORSEMENT_COLLECTION);

            // Group endorsements by policyId and process each group
            return endorsements.stream()
                    .collect(Collectors.groupingBy(doc -> doc.getString(POLICY_ID)))
                    .entrySet()
                    .stream()
                    .flatMap(entry -> filterEndorsementsFromLastPolicyBind(entry.getValue(), entry.getKey()).stream())
                    .collect(Collectors.toList());
        } finally {
            log.info(LoggingUtils.logMethodExit());
        }
    }

    /**
     * Filters a list of policy endorsements to return only those created after the last "Policy Bind" endorsement.
     *
     * The method first sorts the endorsements by their creation date. Then, it identifies the most recent
     * "Policy Bind" endorsement based on the transaction description. Finally, it returns all endorsements
     * from that point onward, inclusive of the "Policy Bind" endorsement.
     *
     * @param policyEndorsements The list of policy endorsements represented as Documents.
     * @param policyId ID of the policy
     * @return A filtered list of policy endorsements containing all endorsements from the last "Policy Bind" onwards.
     */
    private List<Document> filterEndorsementsFromLastPolicyBind(List<Document> policyEndorsements, String policyId) {
        // Sort endorsements by creation date
        policyEndorsements.sort(Comparator.comparing(doc -> doc.getDate(CREATED_AT)));

        // Find the last "Policy bind" endorsement using streams
        int lastPolicyBindIndex = IntStream.range(0, policyEndorsements.size())
                //"transactionDescription": "Policy Bind",
                .filter(i -> {
                    String type = policyEndorsements.get(i).getString(ENDORSEMENT_TYPE);
                    String transactionType = policyEndorsements.get(i).getString(TRANSACTION_DESCRIPTION);
                    log.info("PolicyId: {}, Index: {}, Transaction Type: {}, EndorsementType : {}", policyId, i, transactionType, type);
                    return "Policy Bind".equalsIgnoreCase(transactionType);
                })
                .reduce((first, second) -> second)
                .orElse(-1);

        // Return all endorsements after last "Policy bind"
        List<Document> filteredEndorsement =  lastPolicyBindIndex != -1
                ? policyEndorsements.subList(lastPolicyBindIndex, policyEndorsements.size())
                : policyEndorsements;

        log.info("Found {} policy endorsements after last policy bind (inclusive) for policy id : {}", filteredEndorsement.size(), policyId);

        return filterPolicyEndorsementsForCancelReWrite(filteredEndorsement, policyId);
    }

    /**
     * Filters a list of policy endorsements to return only those created after the last "REWRITE" endorsement.
     *
     * The method first sorts the endorsements by their creation date. Then, it identifies the most recent
     * "REWRITE" endorsement based on the endorsement type. Finally, it returns all endorsements from that
     * point onward, inclusive of the "REWRITE" endorsement. If no "REWRITE" endorsement is found, it returns
     * the entire list.
     *
     * @param policyEndorsements The list of policy endorsements represented as Documents.
     * @param policyId ID of the policy
     * @return A filtered list of policy endorsements containing all endorsements from the last "REWRITE" onwards,
     *         or the entire list if no "REWRITE" endorsement is found.
     */
    private List<Document> filterPolicyEndorsementsForCancelReWrite(List<Document> policyEndorsements, String policyId) {
        // Sort endorsements by creation date (Ascending)
        policyEndorsements.sort(Comparator.comparing(doc -> doc.getDate(CREATED_AT)));

        // Find the last rewrite endorsement using streams
        int lastRewriteIndex = IntStream.range(0, policyEndorsements.size())
                .filter(i -> {
                    String type = policyEndorsements.get(i).getString(ENDORSEMENT_TYPE);
                    log.info("PolicyId: {}, Index: {}, Endorsement Type: {}", policyId, i, type);
                    return "REWRITE".equalsIgnoreCase(type);
                })
                .reduce((first, second) -> second)
                .orElse(-1);

        // Validate the found index
        if (lastRewriteIndex == -1) {
            log.warn("No REWRITE endorsement found for policyId: {}", policyId);
            return policyEndorsements; // Return empty if no REWRITE is found
        }

        // Return endorsements from the last REWRITE onwards (inclusive)
        List<Document> filteredByRewrite = policyEndorsements.subList(lastRewriteIndex, policyEndorsements.size());

        log.info("PolicyId: {}, Found {} endorsements from last REWRITE onwards.", policyId, filteredByRewrite.size());
        return filteredByRewrite;
    }
}
