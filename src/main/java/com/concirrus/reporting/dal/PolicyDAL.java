package com.concirrus.reporting.dal;

import org.bson.Document;
import org.springframework.data.domain.PageRequest;

import java.time.LocalDate;
import java.util.List;

public interface PolicyDAL {

    List<Document> getPoliciesBy(LocalDate startDate, LocalDate endDate, Boolean isPremiumPaid, String clientId, PageRequest pageRequest);

    List<Document> getPoliciesByInvoiceDate(LocalDate startDate, LocalDate endDate, String clientId, PageRequest pageRequest);

    List<Document> getPoliciesByEffectiveDate(LocalDate startDate, LocalDate endDate, String clientId, PageRequest pageRequest);
}
