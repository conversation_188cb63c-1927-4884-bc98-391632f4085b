package com.concirrus.reporting.dal;

import com.concirrus.reporting.entity.ReportRequestInfo;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.PageRequest;

import java.time.LocalDate;
import java.util.List;

public interface ReportingDAL {
    ReportRequestInfo addReportRequest(ReportRequestInfo reportRequestInfo);

    ReportRequestInfo updateReportRequest(String requestId, String clientId, ReportRequestInfo updateReportRequestInfo);

    ReportRequestInfo getReportRequestById(String requestId, String clientId);

    Pair<Long, List<ReportRequestInfo>> getPairOfTotalCountAndReportRequestInfoList(PageRequest pageRequest, String clientId, LocalDate date);

    List<ReportRequestInfo> getReportRequestInfoListBeforeDate(PageRequest pageRequest, String clientId, LocalDate date);

    void deleteReportRequestInfoByIds(List<String> ids);
}
