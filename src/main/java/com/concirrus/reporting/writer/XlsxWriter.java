package com.concirrus.reporting.writer;

import com.concirrus.reporting.model.XlsxField;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

public interface XlsxWriter {
    <T> byte[] generateExcel(List<T> data);
    <T> int appendRowsToSheet(List<T> data, Workbook workbook, Sheet sheet, int startRow, List<XlsxField> xlsColumnFields) throws InvocationTargetException, NoSuchMethodException, IllegalAccessException;
    <T> List<XlsxField> createHeader(T data, Workbook workbook, Sheet sheet);
}
