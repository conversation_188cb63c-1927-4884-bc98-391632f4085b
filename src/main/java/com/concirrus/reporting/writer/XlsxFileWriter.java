package com.concirrus.reporting.writer;

import com.concirrus.reporting.annotation.XlsxSheet;
import com.concirrus.reporting.annotation.XlsxSingleField;
import com.concirrus.reporting.model.XlsxField;
import com.concirrus.reporting.model.report.ASUAgingReportModel;
import com.concirrus.reporting.utils.LoggingUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

@Service
@Slf4j
public class XlsxFileWriter implements XlsxWriter {

    /**
     * Generates an Excel file from the provided list of data elements and returns the content as a byte array.
     *
     * @param data The list of data elements to be written into the Excel file.
     * @param <T>  The type of elements within the provided list.
     * @return A byte array representing the generated Excel file content.
     */
    @Override
    public <T> byte[] generateExcel(List<T> data) {
        log.info(LoggingUtils.logMethodEntry());
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream(); Workbook workbook = new XSSFWorkbook()) {
            write(data, bos, workbook);

            log.info(LoggingUtils.logMethodExit());
            return bos.toByteArray();

        } catch (Exception e) {
            log.error("Generating users xls file failed", e);
        }
        log.info(LoggingUtils.logMethodExit());
        return new byte[0];
    }

    @Override
    public  <T> int appendRowsToSheet(List<T> data, Workbook workbook, Sheet sheet, int startRow, List<XlsxField> xlsColumnFields) throws InvocationTargetException, NoSuchMethodException, IllegalAccessException {
        if (CollectionUtils.isEmpty(data)) {
            log.error("No data received to write Xls file.. current row : {}", startRow);
            return startRow;
        }

        Font genericFont = getGenericFont(workbook);
        CellStyle genericStyle = getLeftAlignedCellStyle(workbook, genericFont);
        CellStyle floatStyle = setFloatCellStyle(workbook);
        CellStyle centerAlignedStyle = getCenterAlignedCellStyle(workbook);

        Class<?> clazz = data.get(0).getClass();
        int currentRow = startRow;

        for (T record : data) {
            CellStyle modifiedGenericStyle = genericStyle;
            CellStyle modifiedFloatStyle = floatStyle;
            CellStyle modifiedCenterAlignedStyle = centerAlignedStyle;

            // Special handling for ASUAgingReportModel coloring
            if (record instanceof ASUAgingReportModel) {
                modifiedGenericStyle = createCellStyleFromExisting(workbook, genericStyle);
                modifiedFloatStyle = createCellStyleFromExisting(workbook, floatStyle);
                modifiedCenterAlignedStyle = createCellStyleFromExisting(workbook, centerAlignedStyle);

                try {
                    Method xlsMethod = getMethod(clazz, new XlsxField("dayPastDue", null, 0));
                    Object dayPastDueValue = xlsMethod.invoke(record);

                    if (dayPastDueValue instanceof Long) {
                        long days = (Long) dayPastDueValue;
                        if (days > 30 && days <= 60) {
                            // Set color(=LIGHT_ORANGE) to the row
                            modifiedFloatStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
                            modifiedFloatStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                            modifiedCenterAlignedStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
                            modifiedCenterAlignedStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                            modifiedGenericStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
                            modifiedGenericStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                        } else if (days > 60) {

                            // Set color(=RED) to the row
                            modifiedFloatStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
                            modifiedFloatStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                            modifiedCenterAlignedStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
                            modifiedCenterAlignedStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                            modifiedGenericStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
                            modifiedGenericStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                        }
                    }
                } catch (Exception e) {
                    log.error("Error applying row color: ", e);
                }
            }

            createDataRow(workbook, sheet, currentRow++, xlsColumnFields, clazz, modifiedFloatStyle, modifiedCenterAlignedStyle, modifiedGenericStyle, record);
        }
        return currentRow;
    }

    /**
     * Writes data from the provided list into an Excel workbook, populating columns based on the metadata of the POJO class.
     *
     * @param data     The list of data elements to be written into the Excel workbook.
     * @param bos      The ByteArrayOutputStream to write the generated Excel data.
     * @param workbook The Workbook instance where data will be written.
     * @param <T>      The type of elements within the provided list.
     */
    public <T> void write(List<T> data, ByteArrayOutputStream bos, Workbook workbook) {
        log.info(LoggingUtils.logMethodEntry());
        if (data.isEmpty()) {
            log.error("No data received to write Xls file..");
            return;
        }

        long start = System.currentTimeMillis();

        //setting up the basic styles for the workbook
        Font boldFont = getBoldFont(workbook);
        Font genericFont = getGenericFont(workbook);
        CellStyle headerStyle = getLeftAlignedCellStyle(workbook, boldFont);
        CellStyle floatStyle = setFloatCellStyle(workbook);
        CellStyle centerAlignedStyle = getCenterAlignedCellStyle(workbook);
        CellStyle genericStyle = getLeftAlignedCellStyle(workbook, genericFont);

        try {
            // using POJO class metadata for the sheet name
            XlsxSheet annotation = data.get(0).getClass().getAnnotation(XlsxSheet.class);
            String sheetName = annotation.value();
            Sheet sheet = workbook.createSheet(sheetName);

            // Get class of the passed dataset
            Class<?> clazz = data.get(0).getClass();

            // Get the metadata for each field of the POJO class into a list
            List<XlsxField> xlsColumnFields = getXlsxFieldsOfClass(clazz);

            // fetch all headers
            String[] headers = getColumnTitles(xlsColumnFields);

            // create header
            createHeaderRow(sheet, headers, headerStyle);

            int rowCount = 1;

            // looping into data
            for (T record : data) {

                CellStyle modifiedfloatStyle = floatStyle;
                CellStyle modifiedCenterAlignedStyle = centerAlignedStyle;
                CellStyle modifiedGenericStyle = genericStyle;


                // asu_aging report only
                if (record instanceof ASUAgingReportModel) {

                    modifiedfloatStyle = createCellStyleFromExisting(workbook, floatStyle);
                    modifiedCenterAlignedStyle = createCellStyleFromExisting(workbook, centerAlignedStyle);
                    modifiedGenericStyle = createCellStyleFromExisting(workbook, genericStyle);

                    // based on dayPastDue, we decide the row-cell color
                    Method xlsMethod = getMethod(clazz, new XlsxField("dayPastDue", null, 0));
                    Object dayPastDueValue = xlsMethod.invoke(record, (Object[]) null);

                    if (ObjectUtils.isNotEmpty(dayPastDueValue)) {

                        if(dayPastDueValue instanceof Long){
                            long days = (Long) dayPastDueValue;
                            if (days > 30 && days <= 60) {

                                // Set color(=LIGHT_ORANGE) to the row
                                modifiedfloatStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
                                modifiedfloatStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                                modifiedCenterAlignedStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
                                modifiedCenterAlignedStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                                modifiedGenericStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
                                modifiedGenericStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                            } else if (days > 60 && days <= 90) {

                                // Set color(=RED) to the row
                                modifiedfloatStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
                                modifiedfloatStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                                modifiedCenterAlignedStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
                                modifiedCenterAlignedStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                                modifiedGenericStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
                                modifiedGenericStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                            }
                        }
                    }
                }

                createDataRow(workbook, sheet, rowCount++, xlsColumnFields, clazz, modifiedfloatStyle, modifiedCenterAlignedStyle, modifiedGenericStyle, record);
            }

            workbook.write(bos);
            log.info("Xls file generated in [{}] seconds", processTime(start));
            log.info(LoggingUtils.logMethodExit());
        } catch (Exception e) {
            log.info("Xls file write failed", e);
        }
    }


    /**
     * Extracts column titles from a list of XlsxField objects and returns them as an array of Strings.
     *
     * @param xlsColumnFields A list of XlsxField objects containing metadata for columns.
     * @return An array of Strings representing the column titles sorted by cell index.
     */
    private String[] getColumnTitles(List<XlsxField> xlsColumnFields) {
        return xlsColumnFields.stream().sorted(Comparator.comparingInt(XlsxField::getCellIndex)).map(XlsxField::getLabel).toArray(String[]::new);
    }

    /**
     * Creates a header row in the specified sheet with the provided column titles and cell style.
     *
     * @param sheet        The sheet in which the header row needs to be created.
     * @param columnTitles An array of Strings representing the column titles.
     * @param headerStyle  The CellStyle to be applied to the header cells.
     */
    private void createHeaderRow(Sheet sheet, String[] columnTitles, CellStyle headerStyle) {
        Row mainRow = sheet.createRow(0);

        for (int i = 0; i < columnTitles.length; i++) {
            Cell columnTitleCell = mainRow.createCell(i);
            columnTitleCell.setCellStyle(headerStyle);
            columnTitleCell.setCellValue(columnTitles[i]);
        }
    }

    /**
     * Creates a data row in the specified sheet based on the provided metadata, applying different cell styles
     * to each cell based on the type of data, for a given record of type T.
     *
     * @param workbook           The workbook instance for cell style creation.
     * @param sheet              The sheet in which the data row needs to be created.
     * @param rowCount           The row index where the data row needs to be inserted.
     * @param xlsColumnFields    A list of XlsxField objects representing metadata for each column.
     * @param clazz              The Class object representing the type of data in the record.
     * @param floatStyle         The CellStyle for cells containing float data.
     * @param centerAlignedStyle The CellStyle for center-aligned cells.
     * @param genericStyle       The generic CellStyle for other types of data cells.
     * @param record             The record of type T for which the data row is created.
     * @throws InvocationTargetException When an exception occurs during method invocation.
     * @throws NoSuchMethodException     When a specified method is not found.
     * @throws IllegalAccessException    When access to a class, method, or field is not permitted.
     */
    private <T> void createDataRow(Workbook workbook, Sheet sheet, int rowCount, List<XlsxField> xlsColumnFields, Class<?> clazz, CellStyle floatStyle, CellStyle centerAlignedStyle, CellStyle genericStyle, T record) throws InvocationTargetException, NoSuchMethodException, IllegalAccessException {
        Row mainRow = sheet.createRow(rowCount);

        for (XlsxField xlsColumnField : xlsColumnFields) {
            writeRow(mainRow, xlsColumnField, clazz, floatStyle, centerAlignedStyle, genericStyle, record, workbook);
        }
    }

    /**
     * Creates a new CellStyle based on an existing CellStyle from the provided Workbook.
     *
     * @param workbook      The Workbook object from which to create the new CellStyle.
     * @param existingStyle The existing CellStyle object to clone properties from.
     * @return A new CellStyle object with properties cloned from the existingStyle.
     */
    private CellStyle createCellStyleFromExisting(Workbook workbook, CellStyle existingStyle) {
        CellStyle newStyle = workbook.createCellStyle();
        newStyle.cloneStyleFrom(existingStyle);
        return newStyle;
    }

    /**
     * Writes data into a specific cell of the row based on the XlsxField metadata and the provided record of type T.
     *
     * @param mainRow            The Row object in which the cell needs to be created.
     * @param xlsColumnField     The XlsxField metadata representing the column.
     * @param clazz              The Class object representing the type of data in the record.
     * @param floatStyle         The CellStyle for cells containing float data.
     * @param centerAlignedStyle The CellStyle for center-aligned cells.
     * @param genericStyle       The generic CellStyle for other types of data cells.
     * @param record             The record of type T containing the data to be written.
     * @param workbook           The workbook instance for cell style creation.
     * @throws NoSuchMethodException     When a specified method is not found.
     * @throws InvocationTargetException When an exception occurs during method invocation.
     * @throws IllegalAccessException    When access to a class, method, or field is not permitted.
     */
    private <T> void writeRow(Row mainRow, XlsxField xlsColumnField, Class<?> clazz, CellStyle floatStyle, CellStyle centerAlignedStyle, CellStyle genericStyle, T record, Workbook workbook) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        Cell newCell = mainRow.createCell(xlsColumnField.getCellIndex());
        Method xlsMethod = getMethod(clazz, xlsColumnField);
        Object xlsObjValue = xlsMethod.invoke(record, (Object[]) null);
        setCellValue(newCell, xlsObjValue, floatStyle, centerAlignedStyle, genericStyle, workbook);
    }

    /**
     * Sets the value of the provided cell based on the given object value and cell styles.
     *
     * @param cell               The Cell object where the value needs to be set.
     * @param objValue           The value to be set in the cell (can be of type String, Long, Integer, Double, or Boolean).
     * @param floatStyle         The CellStyle for cells containing float data.
     * @param centerAlignedStyle The CellStyle for center-aligned cells.
     * @param genericStyle       The generic CellStyle for other types of data cells.
     * @param workbook           The workbook instance for hyperlink creation.
     */
    private void setCellValue(Cell cell, Object objValue, CellStyle floatStyle, CellStyle centerAlignedStyle, CellStyle genericStyle, Workbook workbook) {
        if (Objects.isNull(objValue)) {
            return;
        }

        Hyperlink link = workbook.getCreationHelper().createHyperlink(HyperlinkType.URL);

        if (objValue instanceof String) {
            String cellValue = (String) objValue;
            cell.setCellStyle(genericStyle);
            if (cellValue.startsWith("https://") || cellValue.startsWith("http://")) {
                link.setAddress(cellValue);
                cell.setCellValue(cellValue);
                cell.setHyperlink(link);
            } else {
                cell.setCellValue(cellValue);
            }
        } else if (objValue instanceof Long) {
            cell.setCellValue((Long) objValue);
            cell.setCellStyle(genericStyle);
        } else if (objValue instanceof Integer) {
            cell.setCellValue((Integer) objValue);
            cell.setCellStyle(genericStyle);
        } else if (objValue instanceof Double cellValue) {
            cell.setCellStyle(floatStyle);
            cell.setCellValue(cellValue);
        } else if (objValue instanceof Boolean) {
            cell.setCellStyle(centerAlignedStyle);
            cell.setCellValue((Boolean) objValue);
        }
    }


    /**
     * Retrieves XlsxField metadata information for fields annotated with @XlsxSingleField
     * within the provided class and returns a list of XlsxField objects.
     *
     * @param clazz The Class object representing the class for which XlsxField metadata needs to be retrieved.
     * @return A list of XlsxField objects containing metadata information for annotated fields.
     */
    private static List<XlsxField> getXlsxFieldsOfClass(Class<?> clazz) {
        List<XlsxField> xlsColumnFields = new ArrayList<>();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {

            XlsxField xlsColumnField = new XlsxField();
            XlsxSingleField xlsField = field.getAnnotation(XlsxSingleField.class);

            if (Objects.nonNull(xlsField)) {
                xlsColumnField.setCellIndex(xlsField.columnIndex());
                xlsColumnField.setLabel(xlsField.label());
            }

            xlsColumnField.setFieldName(field.getName());
            xlsColumnFields.add(xlsColumnField);
        }
        return xlsColumnFields;
    }

    /**
     * Capitalizes the first letter of the given string and returns the modified string.
     *
     * @param s The input string to be capitalized.
     * @return A string with the first letter capitalized or the original string if empty.
     */
    private static String capitalize(String s) {
        if (s.isEmpty()) return s;
        return s.substring(0, 1).toUpperCase() + s.substring(1);
    }


    /**
     * Retrieves the appropriate method based on the XlsxField metadata and the class.
     *
     * @param clazz          The Class object representing the class containing the method.
     * @param xlsColumnField The XlsxField metadata specifying the field name.
     * @return The Method object retrieved based on the provided metadata.
     * @throws NoSuchMethodException When a specified method is not found.
     */
    private Method getMethod(Class<?> clazz, XlsxField xlsColumnField) throws NoSuchMethodException {
        Method method;
        try {
            method = clazz.getMethod("get" + capitalize(xlsColumnField.getFieldName()));
        } catch (NoSuchMethodException nme) {
            method = clazz.getMethod(xlsColumnField.getFieldName());
        }

        return method;
    }

    private long processTime(long start) {
        return (System.currentTimeMillis() - start) / 1000;
    }

    /**
     * Automatically resizes columns in the provided sheet to fit the content width.
     *
     * @param sheet       The Sheet in which columns need to be auto-sized.
     * @param noOfColumns The number of columns to be resized.
     */
    private void autoSizeColumns(Sheet sheet, int noOfColumns) {
        for (int i = 0; i < noOfColumns; i++) {
            sheet.autoSizeColumn((short) i);
        }
    }

    /**
     * Creates and configures a CellStyle for float formatting in the provided workbook.
     *
     * @param workbook The Workbook instance in which the CellStyle needs to be created.
     * @return The CellStyle configured for float formatting.
     */
    private CellStyle setFloatCellStyle(Workbook workbook) {
        CellStyle floatStyle = workbook.createCellStyle();
        floatStyle.setWrapText(true);
        DataFormat df = workbook.createDataFormat();
        floatStyle.setDataFormat(df.getFormat("#0.00"));
        return floatStyle;
    }

    /**
     * Retrieves a bold Font style based on a generic Font from the provided Workbook.
     *
     * @param workbook The Workbook instance from which to retrieve the Font style.
     * @return The Font style configured as bold.
     */
    private Font getBoldFont(Workbook workbook) {
        Font font = getGenericFont(workbook);
        font.setBold(true);
        return font;
    }

    /**
     * Retrieves a generic Font style with specific attributes from the provided Workbook.
     *
     * @param workbook The Workbook instance from which to retrieve the generic Font style.
     * @return The Font style configured with generic attributes such as font size, name, and color.
     */
    private Font getGenericFont(Workbook workbook) {
        Font font = workbook.createFont();
        font.setFontHeight((short) (10 * 20));
        font.setFontName("Calibri");
        font.setColor(IndexedColors.BLACK.getIndex());
        return font;
    }

    /**
     * Retrieves a CellStyle configured for center alignment and specific border settings from the provided Workbook.
     *
     * @param workbook The Workbook instance from which to retrieve the CellStyle.
     * @return The CellStyle configured with center alignment and minimal border settings.
     */
    private CellStyle getCenterAlignedCellStyle(Workbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
        cellStyle.setBorderTop(BorderStyle.NONE);
        cellStyle.setBorderBottom(BorderStyle.NONE);
        cellStyle.setBorderLeft(BorderStyle.NONE);
        cellStyle.setBorderRight(BorderStyle.NONE);
        return cellStyle;
    }

    /**
     * Retrieves a CellStyle configured for left alignment and specific border settings with the given font
     * from the provided Workbook.
     *
     * @param workbook The Workbook instance from which to retrieve the CellStyle.
     * @param font     The Font style to be set within the CellStyle.
     * @return The CellStyle configured with left alignment, the provided Font, and minimal border settings.
     */
    private CellStyle getLeftAlignedCellStyle(Workbook workbook, Font font) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        cellStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
        cellStyle.setBorderTop(BorderStyle.NONE);
        cellStyle.setBorderBottom(BorderStyle.NONE);
        cellStyle.setBorderLeft(BorderStyle.NONE);
        cellStyle.setBorderRight(BorderStyle.NONE);
        return cellStyle;
    }

    public <T> List<XlsxField> createHeader(T data, Workbook workbook, Sheet sheet) {
        if (ObjectUtils.isEmpty(data)) {
            log.error("No data to create header.");
            return Collections.emptyList();
        }

        Font boldFont = getBoldFont(workbook);
        CellStyle headerStyle = getLeftAlignedCellStyle(workbook, boldFont);

        Class<?> clazz = data.getClass();
        List<XlsxField> xlsColumnFields = getXlsxFieldsOfClass(clazz);
        String[] headers = getColumnTitles(xlsColumnFields);

        createHeaderRow(sheet, headers, headerStyle);

        return xlsColumnFields;
    }
}
