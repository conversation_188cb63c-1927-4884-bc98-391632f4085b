package com.concirrus.reporting.annotation;

import com.concirrus.reporting.model.exception.AccessDeniedException;
import com.concirrus.reporting.model.exception.BadRequestException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static com.concirrus.reporting.constant.ReportConstant.COMMA;
import static com.concirrus.reporting.constant.RequestConstant.*;
import static org.apache.commons.lang3.StringUtils.SPACE;

@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class UserRoleValidationAspect {

    private final RestTemplate restTemplate;

    @Value("${external.endpoints.authorisation-service.baseUrl}")
    private String authorisationSvcBaseURL;

    private static final List<String> USER_ROLE_VALIDATION_EXCLUDED_LOBS = List.of("APPLIED_AVIATION");

    /**
     * Validates whether the user has the required roles to access the specified resource.
     *
     * This method intercepts the execution of a method annotated with {@link UserRoleValidation} to check
     * if the user has the necessary permissions based on roles provided in the request headers. It performs the following steps:
     *
     * 1. Retrieves the HTTP request and extracts the URI and HTTP method to form the endpoint.
     * 2. Fetches user ID and client ID from request headers.
     * 3. Retrieves and validates user roles from request headers. If roles are missing or empty, an {@link BadRequestException} is thrown.
     * 4. Prepares a request body for the authorization service, including action, entity, product, and user roles.
     * 5. Calls the authorization service to check if the user has access. Logs the result and either proceeds with the original method
     *    or throws an {@link AccessDeniedException} if access is denied.
     *
     * @param joinPoint the join point representing the method execution being intercepted
     * @param userRoleValidation the annotation containing the action and entity to be validated
     * @return the result of the original method if access is granted
     * @throws Throwable if the original method throws an exception or if access is denied
     */
    @Around("@annotation(userRoleValidation)")
    public Object validateRole(ProceedingJoinPoint joinPoint, UserRoleValidation userRoleValidation) throws Throwable {
        // Get the HTTP request
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

        // Extract the URI part (excluding the context path)
        String requestURI = request.getRequestURI();
        String apiEndpoint = request.getMethod() + SPACE + requestURI;

        // Fetch userId from request header
        String userId = request.getHeader(USER_ID);

        // Fetch clientId from request header
        String clientId = request.getHeader(CLIENT_ID);

        // Fetch line-of-business from request header
        String lob = request.getHeader(LINE_OF_BUSINESS);

        if(USER_ROLE_VALIDATION_EXCLUDED_LOBS.contains(lob)){
            log.info("{} is in the excluded lob list for user role validation, hence skipping the validation.", lob);
            log.info("Access granted to user id : '{}' for endpoint : '{}'", userId, apiEndpoint);
            return joinPoint.proceed();
        }

        // Fetch roles from request header
        String userRoles = request.getHeader(USER_ROLES); // roles are sent in a comma-separated format

        if (StringUtils.isBlank(userRoles)) {
            log.error("User ID '{}': Found empty or missing roles. User roles: '{}' (should not be empty).", userId, userRoles);
            throw new BadRequestException("User roles are missing or empty.");
        }

        log.info("Retrieved roles '{}' from the header for user ID: {}", userRoles, userId);

        List<String> userRoleList = Stream.of(userRoles.split(COMMA)).toList();

        // extract annotation vars
        String action = userRoleValidation.action();
        String entity = userRoleValidation.entity();

        // Prepare the request body for the authorization service
        Map<String, Object> requestBody = new HashMap<>();

        requestBody.put("action", action);
        requestBody.put("entity", entity);
        requestBody.put("product", "CORE_SYSTEM");
        requestBody.put("userRoles", userRoleList);
        requestBody.put("state", "BOUND"); // we are sending the default state as "BOUND" as state doesn't matter for report

        // Make the POST call to the authorization service
        boolean hasAccess = validateUserRoles(requestBody, userId, clientId);

        // If access is granted, proceed with the method call
        if (hasAccess) {
            log.info("Access granted to user id : '{}' for endpoint : '{}'", userId, apiEndpoint);
            return joinPoint.proceed();
        } else {
            log.error("Access denied to user id : '{}' for endpoint : '{}'", userId, apiEndpoint);
            throw new AccessDeniedException("Access denied: User does not have permission to perform this action.");
        }
    }

    /**
     * Validates user roles by making a POST request to an authorization service.
     *
     * <p>This method sends a request to the authorisation service to validate if the user has the appropriate roles
     * based on the provided request body, user ID, and client ID. It logs the request details, makes the POST request,
     * and logs the response status. The method assumes that a successful response indicates that access is granted.</p>
     *
     * @param requestBody a {@link Map} containing the details of the request body to be sent to the authorization service.
     *                    This typically includes action, entity, and user roles.
     * @param userId      the ID of the user whose roles are being validated. This is included in the request header.
     * @param clientId    the ID of the client making the request. This is included in the request header.
     *
     * @return {@code true} if the response status code from the authorization service is {@link HttpStatus#OK} (200),
     *         indicating that access is granted; {@code false} otherwise.
     *
     */
    private boolean validateUserRoles(Map<String, Object> requestBody, String userId, String clientId) {
        log.info("validateUserRoles - {}, {}, {}", requestBody, userId, clientId);
        String apiUrl = authorisationSvcBaseURL + "/policy/evaluate";

        try {
            HttpHeaders headers = new HttpHeaders();

            //add userId in the header
            headers.add(USER_ID, userId);

            // add clientId in the header
            headers.add(CLIENT_ID, clientId);

            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // Log the API URL before making the request
            log.info("Making POST request to URL: '{}'", apiUrl);

            ResponseEntity<Void> response = restTemplate.postForEntity(apiUrl, entity, Void.class);

            log.info("Received response from authorisation service: Status Code: '{}'", response.getStatusCode());

            // Assuming a successful response means access is granted
            return response.getStatusCode() == HttpStatus.OK;
        } catch (Exception e) {
            log.error("Error occurred during validating user roles", e);
            return false;
        }
    }
}