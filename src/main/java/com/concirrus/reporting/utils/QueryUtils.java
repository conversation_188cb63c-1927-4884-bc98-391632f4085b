package com.concirrus.reporting.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Objects;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class QueryUtils {

    /**
     * Generates a pagination query for MongoDB based on the provided Query and PageRequest.
     *
     * @param query The original query to be paginated.
     * @param req   The PageRequest specifying the pagination parameters such as page size, page number, and sort order.
     * @return The pagination query with the specified limit, offset, and sort order.
     */
    public static Query getPaginationQueryForMongo(Query query, PageRequest req) {
        final int limit = req.getPageSize();
        final int offset = req.getPageNumber() * req.getPageSize();
        Sort sortOrder = req.getSort();

        if (Objects.isNull(query)) {
            query = new Query();
        }

        query.skip(offset).limit(limit);
        query.with(sortOrder);

        log.info("The pagination query is limit:{}, offset:{}, sort:{}", query.getLimit(), query.getSkip(), query.getSortObject());
        return query;
    }
}
