package com.concirrus.reporting.utils;

import com.concirrus.reporting.model.report.AppliedAviationBordereauReportModel;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.concirrus.reporting.constant.DBConstant.*;
import static com.concirrus.reporting.constant.ReportConstant.*;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class AppliedUtilityMapper {
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("MM/dd/yyyy");

    public static List<AppliedAviationBordereauReportModel> buildAppliedAviationBordereauReportModelList(List<Document> documents) {

        if (CollectionUtils.isEmpty(documents)) {
            return new ArrayList<>();
        }

        return documents.stream()
                .map(AppliedUtilityMapper::buildAppliedAviationBordereauReportModelReport)
                .collect(Collectors.toList());
    }

    public static AppliedAviationBordereauReportModel buildAppliedAviationBordereauReportModelReport(Document document) {
        AppliedAviationBordereauReportModel appliedAviationBordereauReportModel = new AppliedAviationBordereauReportModel();

        String policyBoundDate = document.getString(POLICY_BOUND_DATE);

        policyBoundDate = Objects.nonNull(policyBoundDate) ? LocalDate.parse(policyBoundDate).format(DATE_FORMATTER) : null;
        appliedAviationBordereauReportModel.setReportingMonth(policyBoundDate);

        appliedAviationBordereauReportModel.setUnderwriter(document.getString(UNDERWRITER));

        String renewalStatus = document.getString(RENEWAL_STATUS);
        String transactionType = StringUtils.isNotBlank(renewalStatus) ? RENEWAL : NEW_BUSINESS;

        appliedAviationBordereauReportModel.setTransactionType(transactionType);

        Document quoteInfo = document.get(QUOTE_INFO, Document.class);
        Document policy = quoteInfo.get(POLICY, Document.class);

        appliedAviationBordereauReportModel.setInsuredName(policy.getString(INSURED_NAME));
        appliedAviationBordereauReportModel.setPolicyState(policy.getString(INSURED_STATE_ABBRIVATION));
        appliedAviationBordereauReportModel.setPolicyCountry(UNITED_STATES_ABBR);
        appliedAviationBordereauReportModel.setMailingStreetAddress(policy.getString(INSURED_ADDRESS));
        appliedAviationBordereauReportModel.setMailingCity(policy.getString(INSURED_CITY));
        appliedAviationBordereauReportModel.setMailingState(policy.getString(INSURED_STATE_ABBRIVATION));
        appliedAviationBordereauReportModel.setMailingZip(policy.getString(INSURED_ZIP));
        appliedAviationBordereauReportModel.setMailingCountry(UNITED_STATES_ABBR);
        appliedAviationBordereauReportModel.setPolicyNumber(policy.getString(POLICY_NUMBER));
        appliedAviationBordereauReportModel.setTransactionDate(policyBoundDate);

        appliedAviationBordereauReportModel.setPolicyEffectiveDate(policy.getString(POLICY_EFFECTIVE_DATE));
        appliedAviationBordereauReportModel.setPolicyExpirationDate(policy.getString(POLICY_EXPIRATION_DATE));

        String quoteDate = policy.getString(QUOTE_DATE);
        if (StringUtils.isNotBlank(quoteDate)) {
            quoteDate = LocalDate.parse(quoteDate).format(DATE_FORMATTER);
            appliedAviationBordereauReportModel.setQuoteDate(quoteDate);
        }

        appliedAviationBordereauReportModel.setBoundDate(policyBoundDate);

        String issuedDate = policy.getString(ISSUED_DATE);
        if (StringUtils.isNotBlank(issuedDate)) {
            issuedDate = LocalDate.parse(issuedDate).format(DATE_FORMATTER);
            appliedAviationBordereauReportModel.setIssuedDate(issuedDate);
        }

        appliedAviationBordereauReportModel.setCarrier(policy.getString(CARRIER));

        Document producer = document.get(PRODUCER, Document.class);

        //broker
        appliedAviationBordereauReportModel.setBrokerId(producer.get(LOCATION_ID));
        appliedAviationBordereauReportModel.setBrokerName(producer.get(AGENCY_NAME));
        appliedAviationBordereauReportModel.setBrokerAddress(producer.get(ADDRESS));
        appliedAviationBordereauReportModel.setBrokerCity(producer.get(CITY));
        appliedAviationBordereauReportModel.setBrokerState(producer.get(STATE));
        appliedAviationBordereauReportModel.setBrokerZipcode(producer.get(ZIP));
        appliedAviationBordereauReportModel.setBrokerCountry(UNITED_STATES_ABBR);
        appliedAviationBordereauReportModel.setBrokerCommissionPercentage(producer.get(COMMISSION_PERCENTAGE));

        boolean leadPolicy = policy.getBoolean(LEAD_POLICY, false);
        String leadFollow = leadPolicy ? LEAD : FOLLOW;
        appliedAviationBordereauReportModel.setLeadOrFollow(leadFollow);

        Number quotaShareValue = policy.get(QS_PROPERTY) instanceof Number ? (Number) policy.get(QS_PROPERTY) : 0;
        double quotaSharePercentage = quotaShareValue.doubleValue();

        appliedAviationBordereauReportModel.setQuotaSharePercentage(formatNumber(quotaSharePercentage));

        appliedAviationBordereauReportModel.setProductLine(document.get(PRODUCT));

        appliedAviationBordereauReportModel.setNumberOfAircraft(policy.get(AIRCRAFT_COUNT));

        List<Document> aircrafts = policy.getList(AIRCRAFTS, Document.class);
        int maxLeadLiabilityLimit = Optional.ofNullable(aircrafts)
                .orElseGet(Collections::emptyList)
                .stream()
                .mapToInt(doc -> doc.getInteger(COMBINED_SINGLE_LIMIT, 0))
                .max()
                .orElse(0);

        appliedAviationBordereauReportModel.setLeadLiabilityLimit(maxLeadLiabilityLimit);

        int appliedLiabilityLimit = (int) (maxLeadLiabilityLimit * quotaSharePercentage);

        appliedAviationBordereauReportModel.setAppliedLiabilityLimit(appliedLiabilityLimit);

        appliedAviationBordereauReportModel.setUnderlyingLimit(ZERO_AS_STRING);
        appliedAviationBordereauReportModel.setAggregateLimit(ZERO_AS_STRING);
        appliedAviationBordereauReportModel.setAppliedAggregateLimit(ZERO_AS_STRING);

        int maxInsuredHullValue = Optional.ofNullable(aircrafts)
                .orElseGet(Collections::emptyList)
                .stream()
                .mapToInt(doc -> {
                    Object value = doc.get(INSURED_HULL_VALUE);
                    if (value instanceof Integer) {
                        return (Integer) value;
                    } else if (value instanceof String) {
                        try {
                            return Integer.parseInt((String) value);
                        } catch (NumberFormatException e) {
                            return 0;
                        }
                    } else {
                        return 0;
                    }
                })
                .max()
                .orElse(0);


        int maxHullValue = maxInsuredHullValue;

        if (leadPolicy) {
            Document exposuresManagement = policy.get(EXPOSURES_MANAGEMENT, Document.class);
            int automaticIncreaseForHullValue = Optional.ofNullable(exposuresManagement)
                    .map(d -> d.get(AUTOMATIC_INCR_FOR_HULL_VALUE))
                    .map(v -> {
                        try { return Integer.parseInt(v.toString()); } catch (Exception e) { return 0; }
                    })
                    .orElse(0);

            maxHullValue = Math.max(maxInsuredHullValue, automaticIncreaseForHullValue);
        }

        appliedAviationBordereauReportModel.setMaxHullValue(maxHullValue);

        return appliedAviationBordereauReportModel;
    }

    /**
     * Formats a {@link Number} by removing unnecessary decimal precision.
     * <p>
     * If the given number is a whole number (e.g., 90.00 or 42), it returns
     * the integer part without any decimal (e.g., "90", "42").
     * If the number has decimal precision (e.g., 83.50 or 10.10), it returns
     * the number formatted to exactly two decimal places (e.g., "83.50", "10.10").
     * <p>
     * This method is useful for displaying numbers in a human-friendly format,
     * especially when trailing ".00" should be omitted for whole numbers.
     *
     * @param number the {@link Number} to format (may be {@code Integer}, {@code Double}, etc.)
     * @return a string representation of the formatted number,
     *         or {@code null} if the input is {@code null}
     */
    private static String formatNumber(Number number) {
        if (number == null) {
            return null;
        }

        // Use BigDecimal for precise rounding
        BigDecimal bd = BigDecimal.valueOf(number.doubleValue()).setScale(2, RoundingMode.HALF_UP);

        // If it's effectively a whole number, return as int
        if (bd.stripTrailingZeros().scale() <= 0) {
            return bd.toBigInteger().toString();
        } else {
            return bd.toString();
        }
    }
}
