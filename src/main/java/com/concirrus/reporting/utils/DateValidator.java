package com.concirrus.reporting.utils;


import com.concirrus.reporting.model.exception.BadRequestException;
import com.concirrus.reporting.model.exception.InvalidArgumentsException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.time.LocalDate;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DateValidator {

    /**
     * Validates the relationship between two LocalDate parameters: start date and end date.
     *
     * @param startDate The start date to be validated.
     * @param endDate   The end date to be validated.
     * @throws BadRequestException       If either start date or end date is null.
     * @throws InvalidArgumentsException If the start date is after the end date.
     */
    public static void validateDate(LocalDate startDate, LocalDate endDate) {

        if (ObjectUtils.anyNull(startDate, endDate)) {
            log.error("dates cannot be null");
            throw new BadRequestException("dates cannot be null");
        }

        if (startDate.isAfter(endDate)) {
            log.error("start date must be less than or equal to end date");
            throw new InvalidArgumentsException("start date must be less than or equal to end date");
        }
    }

    public static void validateDateAllData(LocalDate startDate, LocalDate endDate) {

        if (ObjectUtils.isEmpty(startDate)) {
            log.error("Start Date cannot be null");
            throw new BadRequestException("Start Date cannot be null");
        }

        if (startDate.isAfter(endDate)) {
            log.error("start date must be less than or equal to end date");
            throw new InvalidArgumentsException("start date must be less than or equal to end date");
        }
    }
}
