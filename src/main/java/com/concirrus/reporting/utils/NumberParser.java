package com.concirrus.reporting.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.text.DecimalFormat;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class NumberParser {

    /**
     * Parses a given number string, removes any commas, and formats it to 2 decimal places as a double.
     *
     * @param number The input string representing a number, potentially containing commas.
     * @return A double representing the formatted number rounded to 2 decimal places.
     */
    public static double parseToDouble(String number) {
        // Check if the input is null or empty after trimming, and return 0.0 to bypass processing
        if (StringUtils.isBlank(number)) {
            return 0.0;
        }

        // Remove any non-numeric characters, such as commas and dollar signs (except minus sign)
        number = number.replaceAll("[^\\d.-]", StringUtils.EMPTY);

        // Check if the number is empty after removing non-numeric characters
        if (StringUtils.isBlank(number)) {
            return 0.0;
        }

        // Parse the cleaned string to a double
        double parsedNumber = Double.parseDouble(number);

        // Format the parsed double to two decimal places
        DecimalFormat df = new DecimalFormat("#.00");

        // Return the formatted double value
        return Double.parseDouble(df.format(parsedNumber));
    }
}
