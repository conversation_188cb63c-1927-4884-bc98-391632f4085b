package com.concirrus.reporting.utils;

import com.concirrus.reporting.model.report.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.concirrus.reporting.constant.DBConstant.*;
import static com.concirrus.reporting.constant.ReportConstant.*;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class UtilityMapper {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("MM-dd-yyyy");

    private static final List<String> CANCEL_ENDORSEMENT_STATUSES = List.of("REINSTATEMENT", "CANCELLATION", "CANCEL", "REWRITE");

    private static final String NON_MONETARY_ENDORSEMENT_STATUS = "NON_MONETARY";

    /*
     * DecimalFormat for U.S. numbers with commas as thousand separators and two decimal places.
     * The pattern "#,##0.00" ensures:
     * 1. Displays at least one digit before the decimal (e.g., 0.50 instead of .50).
     * 2. Shows whole numbers with two decimal places (e.g., 123 → 123.00).
     * 3. Rounds to two decimal places when necessary (e.g., 123.456 → 123.46).
     * This format enhances clarity, especially for financial values.
     */
    private static final NumberFormat numberFormat = new DecimalFormat("#,##0.00");

    /**
     * Builds a list of {@link BordereauReportModel} objects based on the provided documents, office information, and endorsements.
     *
     * This method processes a list of {@link Document} objects and generates a list of {@link BordereauReportModel} instances.
     * It retrieves relevant office and endorsement data from the provided maps and combines them into a comprehensive report list.
     * If the input document list is empty or null, an empty list is returned.
     *
     * @param documents A list of {@link Document} objects that contain data for generating reports. Must not be null.
     * @param officesMap A map where the key is the office ID and the value is a map of office-related information. May be null.
     * @param endorsementsMap A map where the key is the policy ID and the value is a list of {@link Document} objects representing endorsements. May be null.
     * @return A list of {@link BordereauReportModel} objects representing the generated reports. Never null. If the input document list is empty, an empty list is returned.
     */
    public static List<BordereauReportModel> buildBordereauReportList(List<Document> documents, Map<String, Map<String, String>> officesMap, Map<String, List<Document>> endorsementsMap, Map<String, Document> policyIdToBoundPolicyDocumentMap) {
        log.info("Building the bordereau report list");

        if (CollectionUtils.isEmpty(documents)) {
            return new ArrayList<>();
        }

        List<BordereauReportModel> reports = new ArrayList<>();

        ObjectMapper obj = new ObjectMapper();
        try {
            log.info("policyIdToBoundPolicyDocumentMap: {}", obj.writeValueAsString(policyIdToBoundPolicyDocumentMap));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        documents.forEach(document -> {
            log.info("policyId for policyIdToBoundPolicyDocumentMap: {}", document.getString(POLICY_ID));
            List<BordereauReportModel> report = buildBordereauEndorsementReportList(
                    document,
                    officesMap.get(document.getString(OFFICE_ID)),
                    endorsementsMap.get(document.getString(POLICY_ID)),
                    policyIdToBoundPolicyDocumentMap.get(document.getString(POLICY_ID))
            );
            reports.addAll(report);
        });

       return reports;
    }

    /**
     * Builds a list of {@link BordereauReportModel} objects for a given document based on the provided office information
     * and policy endorsement documents.
     *
     * This method processes a list of policy endorsement documents and generates a corresponding list of
     * {@link BordereauReportModel} instances. If the list of endorsement documents is not empty, each endorsement
     * document is processed to create a report. If the list is empty, a single report is created with a null endorsement.
     *
     * @param document The {@link Document} representing the main document for which reports are being generated. Must not be null.
     * @param officeMap A map where the key is the office ID and the value is office-related information. May be null.
     * @param policyEndorsementDocuments A list of {@link Document} objects representing endorsements for the policy. May be null.
     * @return A list of {@link BordereauReportModel} objects representing the generated reports. If {@code policyEndorsementDocuments}
     *         is empty or null, the list contains a single report with a null endorsement. The returned list is never null.
     */
    public static List<BordereauReportModel> buildBordereauEndorsementReportList(Document document, Map<String, String> officeMap, List<Document> policyEndorsementDocuments, Document boundPolicyDocument) {

        if(CollectionUtils.isNotEmpty(policyEndorsementDocuments)){
            log.info("Found {} endorsements for policy ID : {} ", policyEndorsementDocuments.size(), document.getString(POLICY_ID));

            List<BordereauReportModel> bordereauReportEndorsementDataList = policyEndorsementDocuments.stream()
                    .filter(ed -> {
                        String endorsementType = ed.getString(ENDORSEMENT_TYPE);
                        if (REINSTATEMENT.equals(endorsementType)) {
                            Document policyDoc = (Document) ed.getOrDefault(UPDATED_POLICY_INFO, new Document());
                            Map updatedPremiumDetails = (Map) policyDoc.getOrDefault(PREMIUM_DETAILS, new HashMap<>());
                            Double totalPremium = NumberParser.parseToDouble(String.valueOf(updatedPremiumDetails.get(TOTAL_PREMIUM)));

                            return totalPremium.compareTo(0D) > 0;
                        }
                        return true;
                    })
                    .map(ed -> buildBordereauEndorsementReport(document, officeMap, ed, boundPolicyDocument, true))
                    .collect(Collectors.toList());

//            BordereauReportModel bordereauReportPolicyData = buildBordereauEndorsementReport(document, officeMap, null, boundPolicyDocument, true);
//            bordereauReportEndorsementDataList.add(bordereauReportPolicyData);
            return bordereauReportEndorsementDataList;
        }
        log.info("No endorsements found for policy ID: {}", document.getString(POLICY_ID));

        // case - when no endorsement found for policy
        return Collections.singletonList(buildBordereauEndorsementReport(document, officeMap, null, boundPolicyDocument, false));
    }

    /**
     * Builds a {@link BordereauReportModel} object for a given document based on the provided office information
     * and a policy endorsement document.
     *
     * This method generates a single {@link BordereauReportModel} instance by processing the main document,
     * the office information, and the specific policy endorsement document. It combines these inputs to create
     * a comprehensive report model.
     *
     * @param document The {@link Document} representing the main document for which the report is being generated. Must not be null.
     * @param officeMap A map where the key is the office ID and the value is office-related information. May be null.
     * @param policyEndorsementDocument The {@link Document} representing a specific policy endorsement. May be null.
     * @return A {@link BordereauReportModel} object representing the generated report.
     */
    public static BordereauReportModel buildBordereauEndorsementReport(Document document, Map<String, String> officeMap, Document policyEndorsementDocument, Document boundPolicyDocumentPayload, boolean isPolicyWithEndorsement) {
        BordereauReportModel bordereauReportModel = new BordereauReportModel();

        if(Objects.nonNull(policyEndorsementDocument)){

            // Policy document (if an endorsement exists, the policy information from the endorsement will be used)

            log.info("policyEndorsementDocument: {}", policyEndorsementDocument.toJson());

            document = policyEndorsementDocument.get(UPDATED_POLICY_INFO, Document.class);

            //Previous policy document
            Document previousPolicyDocument = policyEndorsementDocument.get(PREVIOUS_POLICY_INFO, Document.class);

            log.info("previousPolicyDocument: {}", previousPolicyDocument.toJson());

            String endorsementNumber = policyEndorsementDocument.getString(ENDORSEMENT_NUMBER);
            bordereauReportModel.setEndorsementNumber(endorsementNumber);

            Object endorsementSequence = policyEndorsementDocument.get(ENDORSEMENT_SEQUENCE, Object.class);
            bordereauReportModel.setEndorsementSequence(endorsementSequence);

            String transactionDescription = policyEndorsementDocument.getString(TRANSACTION_DESCRIPTION);
            bordereauReportModel.setTransactionDescription(transactionDescription);

            Date endorsementDate = policyEndorsementDocument.get(ENDORSEMENT_EFFECTIVE_DATE, Date.class);
            Map updatedPremiumDetails = document.get(PREMIUM_DETAILS, Map.class);

            if(!"Policy Bind".equalsIgnoreCase(transactionDescription)) {
                LocalDate endorsementLocalDate = endorsementDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                bordereauReportModel.setEndorsementEffectiveDate(endorsementLocalDate.format(DATE_FORMATTER));
                bordereauReportModel.setTotalTechnicalPremium(NOT_APPLICABLE);
                bordereauReportModel.setSoldToTechnicalPercentage(NOT_APPLICABLE);
            } else {
                processPremiumDetail(updatedPremiumDetails, TOTAL_TECHNICAL_PREMIUM, bordereauReportModel::setTotalTechnicalPremium);
                Object soldToTechnicalPercentage = updatedPremiumDetails.get(SOLD_TO_TECHNICAL_PERCENTAGE);
                if (ObjectUtils.isNotEmpty(soldToTechnicalPercentage)) {
                    bordereauReportModel.setSoldToTechnicalPercentage(soldToTechnicalPercentage + PERCENTAGE);
                }
            }


            Object updatedTotalWrittenPremiumObject = updatedPremiumDetails.get(TOTAL_WRITTEN_PREMIUM);

            Double updatedTotalWrittenPremium = 0.0;
            updatedTotalWrittenPremium = calculateAndSetPremium(updatedTotalWrittenPremiumObject,
                    "Total Written Premium", bordereauReportModel::setTotalWrittenPremium);

            Object updatedTriaPremiumObject = updatedPremiumDetails.get(TRIA_PREMIUM);

            Double updatedTriaPremium = 0.0;

            updatedTriaPremium = calculateAndSetPremium(updatedTriaPremiumObject,
                    "Tria Premium", bordereauReportModel::setTriaPremium);

            Object updatedTotalFeesObject = updatedPremiumDetails.get(TOTAL_FEES);

            Double updatedTotalFees = 0.0;

            updatedTotalFees = calculateAndSetPremium(updatedTotalFeesObject,
                    "Fee Amount", bordereauReportModel::setFeeAmount);

            Double totalPremium = updatedTotalWrittenPremium + updatedTriaPremium;
            bordereauReportModel.setTotalPremium(DOLLAR + numberFormat.format(totalPremium));

            //override premium values in case of CANCEL_ENDORSEMENT_STATUSES
            updateCancelEndorsement(policyEndorsementDocument, updatedTotalWrittenPremium, updatedTriaPremium,
                    updatedTotalFees, bordereauReportModel);

            handleCommissionDetails(policyEndorsementDocument, updatedPremiumDetails, bordereauReportModel);

            Object endorsementCreatedDateObject = policyEndorsementDocument.get(CREATED_AT);

            processEndorsementCreatedDate(endorsementCreatedDateObject, bordereauReportModel);

            String policyBoundDateObject = document.get(POLICY_BOUND_DATE, String.class);

            processPolicyBoundDateWithoutPremiumDate(policyBoundDateObject, bordereauReportModel);

        }
        else {

            Map premiumDetails = null;

            Document payload = fetchPayload(boundPolicyDocumentPayload);
            premiumDetails = fetchPremiumDetails(payload, document, isPolicyWithEndorsement);

            bordereauReportModel.setStatus(getStatus(document));

            processPremiumDetail(premiumDetails, TOTAL_WRITTEN_PREMIUM, bordereauReportModel::setTotalWrittenPremium);
            processPremiumDetail(premiumDetails, TRIA_PREMIUM, bordereauReportModel::setTriaPremium);
            processPremiumDetail(premiumDetails, TOTAL_FEES, bordereauReportModel::setFeeAmount);
            processPremiumDetail(premiumDetails, TOTAL_TECHNICAL_PREMIUM, bordereauReportModel::setTotalTechnicalPremium);

            Object soldToTechnicalPercentage = premiumDetails.get(SOLD_TO_TECHNICAL_PERCENTAGE);

            if (ObjectUtils.isNotEmpty(soldToTechnicalPercentage)) {
                bordereauReportModel.setSoldToTechnicalPercentage(soldToTechnicalPercentage + PERCENTAGE);
            }

            Double totalWrittenPremium = getParsedValue(premiumDetails, TOTAL_WRITTEN_PREMIUM);
            Double triaPremium = getParsedValue(premiumDetails, TRIA_PREMIUM);

            Double totalPremium = totalWrittenPremium + triaPremium;
            bordereauReportModel.setTotalPremium(DOLLAR + numberFormat.format(totalPremium));

            processPolicyBoundDate(document.get(POLICY_BOUND_DATE, String.class), bordereauReportModel);

            processCommissionDetails(premiumDetails, bordereauReportModel);

        }

        String policyInceptionDate = document.get(POLICY_INCEPTION_DATE, String.class);
        LocalDate policyStartDate = LocalDate.parse(policyInceptionDate);
        String expirationDateStr = document.get(POLICY_EXPIRATION_DATE, String.class);
        LocalDate policyExpirationDate;
        if (expirationDateStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
            policyExpirationDate = LocalDate.parse(expirationDateStr);
        } else {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd-yyyy");
            policyExpirationDate = LocalDate.parse(expirationDateStr, formatter);
        }

        bordereauReportModel.setEffectiveDate(policyStartDate.format(DATE_FORMATTER));
        bordereauReportModel.setExpiryDate(policyExpirationDate.format(DATE_FORMATTER));

        // policyInfo
        bordereauReportModel.setPolicyNumber(document.get(POLICY_NUMBER));

        // insuredInfo
        Document insuredInfo = document.get("insuredInfo", Document.class);
        bordereauReportModel.setInsuredName(insuredInfo.get(FIRST_NAME));

        Document insuredMailingAddress = insuredInfo.get(MAILING_ADDRESS, Document.class);
        String riskState = insuredMailingAddress.getString(STATE);
        bordereauReportModel.setRiskState(riskState);

        // brokerInfo
        Map brokerInfo = document.get(BROKER_INFO, Map.class);
        String brokerageName = (String) brokerInfo.get(NAME);

        StringBuilder brokerBuilder = new StringBuilder();
        if (StringUtils.isNotEmpty(brokerageName)) {
            brokerBuilder.append(brokerageName).append(StringUtils.SPACE);
        }

        if (MapUtils.isNotEmpty(officeMap)) {
            String officeName = officeMap.get(OFFICE_NAME);
            if (StringUtils.isNotEmpty(officeName)) {
                brokerBuilder.append(officeName);
            }
        }

        bordereauReportModel.setBroker(brokerBuilder.toString());

        bordereauReportModel.setRiskDescription(document.get(DESCRIPTION_OF_OPERATIONS, String.class));

        bordereauReportModel.setProductName(document.get(PRODUCT_NAME, String.class));
        bordereauReportModel.setUnderwriter(document.get(UNDERWRITER_NAME, String.class));
        bordereauReportModel.setCarrier(document.get(CARRIER, String.class));

        String productState = document.get(PRODUCT_STATE, String.class);

        StringBuilder policyLimits = new StringBuilder();

        //exposureDetails
        Map exposureDetails = Optional.ofNullable(document.get(EXPOSURE_DETAILS, Map.class))
                .orElse(new HashMap<>());

        Object eachOccurrenceLimit;
        Object productsCompletedOperationsAggregateLimit;
        Object generalAggregateLimitOtherThanProductsCompletedOperations = null;
        Object otherAggregateLimit = null;
        Object policyAggregateLimitAuto = null;
        Object personalAndAdvertisingInjuryLimit = null;

        if (StringUtils.isNotEmpty(productState) && productState.equalsIgnoreCase(EXCESS)) {
            eachOccurrenceLimit = exposureDetails.get(EACH_OCCURRENCE_LIMIT_EXCESS);
            productsCompletedOperationsAggregateLimit = exposureDetails.get(PRODUCTS_COMPLETED_OPERATIONS_AGGREGATE_LIMIT);
            otherAggregateLimit = exposureDetails.get(OTHER_AGGREGATE_LIMIT);
            policyAggregateLimitAuto = exposureDetails.get(POLICY_AGGREGATE_LIMIT);

        } else {
            eachOccurrenceLimit = exposureDetails.get(EACH_OCCURRENCE_LIMIT_PRIMARY);
            generalAggregateLimitOtherThanProductsCompletedOperations = exposureDetails.get(GENERAL_AGGREGATE_LIMIT);
            productsCompletedOperationsAggregateLimit = exposureDetails.get(PRODUCTS_COMPLETED_OPERATIONS_AGGREGATE_LIMIT);
            personalAndAdvertisingInjuryLimit = exposureDetails.get(PERSONAL_ADVERTISING_INJURY_LIMIT);
        }

        appendLimit(eachOccurrenceLimit, policyLimits);
        appendLimit(generalAggregateLimitOtherThanProductsCompletedOperations, policyLimits);
        appendLimit(productsCompletedOperationsAggregateLimit, policyLimits);
        appendLimit(personalAndAdvertisingInjuryLimit, policyLimits);
        appendLimit(otherAggregateLimit, policyLimits);
        appendLimit(policyAggregateLimitAuto, policyLimits);

        // Check if the input string ends with a slash
        if (policyLimits.toString().endsWith(SLASH)) {
            // Remove the last character (i.e., the slash) from the input string
            bordereauReportModel.setPolicyLimit(policyLimits.substring(0, policyLimits.length() - 1));
        }

        Object attachmentTotalLimitsInclGL = exposureDetails.get(ATTACHMENT_TOTAL_LIMITS_INCL_GL);

        bordereauReportModel.setNumberOfGeneralAggregateReinstatements(exposureDetails.get(NUMBER_OF_GENERAL_AGGREGATE_REINSTATEMENTS));

        bordereauReportModel.setNyftzClass(document.get(NYFTZ_CLASS, String.class));

        //deductibleDetails
        Map deductibleDetails = Optional.ofNullable(document.get(DEDUCTIBLE_DETAILS, Map.class))
                .orElse(new HashMap<>());

        Object deductibleAmount = deductibleDetails.get(DEDUCTIBLE_AMOUNT);
        Object sirAmount = deductibleDetails.get(SIR_AMOUNT);

        String retentionType = (String) deductibleDetails.get(RETENTION_TYPE);

        setRetentionDetails(retentionType, bordereauReportModel, deductibleAmount, sirAmount);

        String attachmentTotalLimitsInclGLInMillions = NOT_APPLICABLE;
        if (Objects.nonNull(attachmentTotalLimitsInclGL)) {
            String limitValue = String.valueOf(attachmentTotalLimitsInclGL).trim();
            if (!limitValue.isEmpty() && !ZERO_AS_STRING.equals(limitValue)) {
                try {
                    // First, ensure it's a valid number and > 0
                    double numericValue = NumberParser.parseToDouble(limitValue);
                    if (numericValue > 0) {
                        String millionsValue = convertToMillions(limitValue);
                        // Check if the converted value is the same as input (meaning conversion failed)
                        if (!millionsValue.equals(limitValue)) {
                            attachmentTotalLimitsInclGLInMillions = millionsValue + "M";
                        }
                    }
                } catch (Exception e) {
                    log.error("Error converting attachment limit to millions: {}", attachmentTotalLimitsInclGL, e);
                }
            }
        }

        bordereauReportModel.setTotalAttachmentLimit(attachmentTotalLimitsInclGLInMillions);

        //rateDetails
        ArrayList<Map<String, Object>> rateDetails = document.get(RATE_DETAILS, ArrayList.class);

        if (CollectionUtils.isNotEmpty(rateDetails)) {
            StringJoiner exposureBasisJoiner = new StringJoiner(SLASH);
            StringJoiner exposureAmountJoiner = new StringJoiner(SLASH);
            StringJoiner rateByExposureBasisJoiner = new StringJoiner(SLASH);

            // Define the list of exposure basis values that require a dollar sign in exposure amount
            List<String> dollarSignExposureBasis = List.of(
                    "$1,000 Construction Costs",
                    "$1,000 Gross Revenue",
                    "$1,000 Management Fees",
                    "$1,000 Payroll"
            );

            rateDetails.forEach(detail -> {
                String exposureBasis = (String) detail.get(EXPOSURE_BASIS);
                Object exposureAmount = detail.get(EXPOSURE_AMOUNT_BY_EXPOSURE_BASIS);
                String rateByExposureBasis = (String) detail.get(RATE_BY_EXPOSURE_BASIS);

                if (StringUtils.isNotEmpty(exposureBasis)) {
                    exposureBasisJoiner.add(exposureBasis);
                }

                if (ObjectUtils.isNotEmpty(exposureAmount)) {
                    try {
                        Double numericValue = NumberParser.parseToDouble(String.valueOf(exposureAmount));
                        String formattedExposureAmount = numberFormat.format(numericValue);

                        if (StringUtils.isNotEmpty(exposureBasis) && dollarSignExposureBasis.contains(exposureBasis)) {
                            formattedExposureAmount = DOLLAR + formattedExposureAmount;
                        }
                        exposureAmountJoiner.add(formattedExposureAmount);
                    } catch (Exception e) {
                        log.error("Failed to format exposure amount: {}", exposureAmount, e);
                        exposureAmountJoiner.add(String.valueOf(exposureAmount));
                    }
                }

                if (StringUtils.isNotEmpty(rateByExposureBasis)) {
                    rateByExposureBasisJoiner.add(rateByExposureBasis);
                }
            });


            bordereauReportModel.setExposureBasis(exposureBasisJoiner.toString());
            bordereauReportModel.setExposureAmount(exposureAmountJoiner.toString());
            bordereauReportModel.setRateByExposureBasis(rateByExposureBasisJoiner.toString());
        }

        //premiumDetails
        Map premiumDetails = document.get(PREMIUM_DETAILS, Map.class);

        bordereauReportModel.setIsoClassificationCode(premiumDetails.get(ISO_CLASSIFICATION_CODE));
        bordereauReportModel.setTotalAutoUnits(premiumDetails.get(TOTAL_AUTOS));

        bordereauReportModel.setTreaty(premiumDetails.get(TREATY));

        String transactionType = document.get(TRANSACTION_TYPE, String.class);

        bordereauReportModel.setRenewalIdentifier(determineRenewalIdentifier(transactionType));

        return bordereauReportModel;
    }

    public static List<AllDataReportModel> buildAllDataReportList(List<Document> documents, Map<String, List<Document>> endorsementsMap) {
        log.info("Building the All data report list");

        if (CollectionUtils.isEmpty(documents)) {
            return new ArrayList<>();
        }

        List<AllDataReportModel> reports = new ArrayList<>();

        documents.forEach(document -> {
            List<AllDataReportModel> report = buildAllDataEndorsementReportList(
                    document,
                    endorsementsMap.get(document.getString(POLICY_ID))
            );
            reports.addAll(report);
        });

        return reports;
    }

    public static List<AllDataReportModel> buildAllDataEndorsementReportList(Document document, List<Document> policyEndorsementDocuments) {

        if(CollectionUtils.isNotEmpty(policyEndorsementDocuments)){
            log.info("Found {} endorsements for policy ID : {} ", policyEndorsementDocuments.size(), document.getString(POLICY_ID));

            return policyEndorsementDocuments.stream()
                    .map(ed -> buildAllDataEndorsementReport(document, ed))
                    .collect(Collectors.toList());
        }
        log.info("No endorsements found for policy ID: {}", document.getString(POLICY_ID));

        // case - when no endorsement found for policy
        return Collections.singletonList(buildAllDataEndorsementReport(document, null));
    }

    public static AllDataReportModel buildAllDataEndorsementReport(Document document, Document policyEndorsementDocument) {
        AllDataReportModel allDataReportModel = new AllDataReportModel();

        document = setDocumentEndorsementData(document, policyEndorsementDocument, allDataReportModel);

        // Simple field mappings
        allDataReportModel.setTransactionType(document.getString("transactionType"));
        allDataReportModel.setStatus(document.getString("status"));
        allDataReportModel.setDefinedStatus(document.getString("definedStatus"));
        allDataReportModel.setPolicyInception(document.getString("policyInceptionDate"));
        allDataReportModel.setPolicyExpiration(document.getString("policyExpirationDate"));
        allDataReportModel.setUnderwriter(document.getString("underwriterName"));
        allDataReportModel.setUnderwriterEmail(document.getString("underwriterEmail"));
        allDataReportModel.setTransactionProcessDate(document.getString("transactionDate"));
        allDataReportModel.setSubmissionCustomerNumber(document.getString("submissionNumber"));
        allDataReportModel.setPriorPolicyNumber(document.getString("priorPolicyNumber"));
        allDataReportModel.setProductType(document.getString("productType"));
        allDataReportModel.setScheduleOfValues(document.getString("scheduleOfValues"));
        allDataReportModel.setNyftzClass(document.getString("nyftzClass"));

        //Invoice fields
        allDataReportModel.setInvoiceNumber(document.getString("invoiceNumber"));
        allDataReportModel.setInvoiceDueDate(document.getString("invoiceDueDate"));

        // Broker Info
        Document brokerInfo = document.get("brokerInfo", Document.class);
        if (brokerInfo != null) {
            allDataReportModel.setBrokerName(brokerInfo.getString("name"));
            allDataReportModel.setBrokerTeam(brokerInfo.getString("team"));
            allDataReportModel.setBrokerSenderName(brokerInfo.getString("senderName"));
            allDataReportModel.setBrokerEmail(brokerInfo.getString("senderEmail"));

            Document mailingAddress = brokerInfo.get(MAILING_ADDRESS, Document.class);
            if (mailingAddress != null) {
                allDataReportModel.setBrokerMailingStreetAddress(mailingAddress.getString("street"));
                allDataReportModel.setBrokerMailingCity(mailingAddress.getString("city"));
                allDataReportModel.setBrokerMailingState(mailingAddress.getString("state"));
                allDataReportModel.setBrokerMailingZipCode(mailingAddress.getString(ZIP_CODE));
            }
        }

        // Insured Info
        Document insuredInfo = document.get("insuredInfo", Document.class);
        if (insuredInfo != null) {
            allDataReportModel.setFirstNamedInsured(insuredInfo.getString("firstName"));
            allDataReportModel.setDba(insuredInfo.getString("dba"));
            allDataReportModel.setNamedInsuredOwner(insuredInfo.getString("owner"));
            allDataReportModel.setNamedInsuredGC(insuredInfo.getString("generalContractor"));

            List<String> otherNamedInsureds = insuredInfo.getList("otherNamedInsureds", String.class);
            if (otherNamedInsureds != null) {
                allDataReportModel.setOtherNamedInsureds(String.join(", ", otherNamedInsureds));
            }

            Document insuredMailingAddress = insuredInfo.get(MAILING_ADDRESS, Document.class);
            if (insuredMailingAddress != null) {
                allDataReportModel.setMailingStreetAddress1(insuredMailingAddress.getString("street1"));
                allDataReportModel.setMailingStreetAddress2(insuredMailingAddress.getString("street2"));
                allDataReportModel.setMailingCity(insuredMailingAddress.getString("city"));
                allDataReportModel.setMailingState(insuredMailingAddress.getString("state"));
                allDataReportModel.setMailingZipCode(insuredMailingAddress.getString(ZIP_CODE));
            }
        }

        // Project Details changes
        List<Document> projectDetails = document.getList("projectDetails", Document.class);

        if (CollectionUtils.isNotEmpty(projectDetails)) {
            String projectNames = projectDetails.stream()
                    .map(detail -> detail.getString("name"))
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(COMMA));

            String projectStreetAddresses = projectDetails.stream()
                    .map(detail -> detail.getString("street"))
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(COMMA));

            String projectCities = projectDetails.stream()
                    .map(detail -> detail.getString("city"))
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(COMMA));

            String projectStates = projectDetails.stream()
                    .map(detail -> detail.getString("state"))
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(COMMA));

            String projectZipCodes = projectDetails.stream()
                    .map(detail -> detail.getString(ZIP_CODE))
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(COMMA));

            String descriptionsOfOperations = projectDetails.stream()
                    .map(detail -> detail.getString("description"))
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(COMMA));

            allDataReportModel.setProjectName(projectNames);
            allDataReportModel.setProjectStreetAddress(projectStreetAddresses);
            allDataReportModel.setProjectCity(projectCities);
            allDataReportModel.setProjectState(projectStates);
            allDataReportModel.setProjectZipCode(projectZipCodes);
            allDataReportModel.setDescriptionOfOperations(descriptionsOfOperations);
        }


        // Exposure Details
        Document exposureDetails = document.get(EXPOSURE_DETAILS, Document.class);

        setExposureLimits(allDataReportModel, exposureDetails,document);

        // Deductible Details
        Document deductibleDetails = document.get("deductibleDetails", Document.class);

        setDeductibleDetails(allDataReportModel, deductibleDetails);

        // Additional Insurance Details
        Document additionalInsuranceDetails = document.get("additionalInsuranceDetails", Document.class);

        // Rate details changes
        List<Document> rateDetails = document.getList("rateDetails", Document.class);

        if (CollectionUtils.isNotEmpty(rateDetails)) {
            String exposureBasis = rateDetails.stream()
                    .map(detail -> detail.getString(EXPOSURE_BASIS))
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(COMMA));

            String exposureBasisDetailed = rateDetails.stream()
                    .map(detail -> detail.getString("exposureBasisDetailed"))
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(COMMA));

            String rateByExposureBasis = rateDetails.stream()
                    .map(detail -> detail.getString(RATE_BY_EXPOSURE_BASIS))
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(COMMA));

            String exposureAmountByExposureBasis = rateDetails.stream()
                    .map(detail -> DOLLAR + numberFormat.format(NumberParser.parseToDouble(detail.getString(EXPOSURE_AMOUNT_BY_EXPOSURE_BASIS))))
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(COMMA));

            allDataReportModel.setRateByExposureBasis(rateByExposureBasis);
            allDataReportModel.setExposureBasisDetailed(exposureBasisDetailed);
            allDataReportModel.setExposureBasis(exposureBasis);
            allDataReportModel.setExposureAmountByExposureBasis(exposureAmountByExposureBasis);
        }

        // Premium Details
        Document premiumDetails = document.get("premiumDetails", Document.class);
        if (premiumDetails != null) {
            allDataReportModel.setTotalWrittenPremium(formatDollarValue(premiumDetails.getString("totalWrittenPremium")));
            allDataReportModel.setIsoClassificationCode(premiumDetails.getString("isoClassificationCode"));
            allDataReportModel.setTriaPremium(formatDollarValue(premiumDetails.getString("triaPremium")));
            allDataReportModel.setTotalNumberOfAutos(premiumDetails.getString("totalAutos"));
            allDataReportModel.setAudit(premiumDetails.get("audit"));
            allDataReportModel.setCommissionPercentage(premiumDetails.get("commissionDetails", Document.class).getString("percentage"));
            allDataReportModel.setCommissionAmount(formatDollarValue(premiumDetails.get("commissionDetails", Document.class).getString("amount")));
            allDataReportModel.setTotalTechnicalPremium(formatDollarValue(premiumDetails.get("totalTechnicalPremium")));
            allDataReportModel.setSoldToTechnicalPercentage(premiumDetails.get("soldToTechnicalPercentage"));
            allDataReportModel.setUnderwriterDebitCreditFactor(premiumDetails.get("underwriterDebitCreditFactor"));

            // Fee details changes
            List<Document> feeDetails = premiumDetails.getList("feeDetails", Document.class);

            if (CollectionUtils.isNotEmpty(feeDetails)) {
                String feeAmounts = feeDetails.stream()
                        .map(detail -> DOLLAR + numberFormat.format(NumberParser.parseToDouble(detail.getString("feeAmount"))))
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(COMMA));

                String feeDescriptions = feeDetails.stream()
                        .map(detail -> detail.getString("feeDescription"))
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(COMMA));

                allDataReportModel.setFeeAmounts(feeAmounts);
                allDataReportModel.setFeeDescription(feeDescriptions);
            }
        }

        // Additional fields
        allDataReportModel.setCarrier(document.getString("carrier"));
        allDataReportModel.setRiskState(document.getList("riskState", String.class).toString());

        return allDataReportModel;
    }

    private static Document setDocumentEndorsementData(Document document, Document policyEndorsementDocument, AllDataReportModel allDataReportModel) {
        if(Objects.nonNull(policyEndorsementDocument)){

            String endorsementNumber = policyEndorsementDocument.getString(ENDORSEMENT_NUMBER);
            allDataReportModel.setEndorsementNumber(endorsementNumber);

            Object endorsementSequence = policyEndorsementDocument.get(ENDORSEMENT_SEQUENCE, Object.class);
            allDataReportModel.setEndorsementSequence(endorsementSequence);

            String transactionDescription = policyEndorsementDocument.getString(TRANSACTION_DESCRIPTION);
            allDataReportModel.setEndorsementTransactionDescription(transactionDescription);

            Date endorsementDate = policyEndorsementDocument.get(ENDORSEMENT_EFFECTIVE_DATE, Date.class);

            LocalDate endorsementLocalDate = endorsementDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            allDataReportModel.setEndorsementEffectiveDate(endorsementLocalDate.format(DATE_FORMATTER));


            // Policy document (if an endorsement exists, the policy information from the endorsement will be used)
            document = policyEndorsementDocument.get(UPDATED_POLICY_INFO, Document.class);
        }
        return document;
    }


    public static List<ASUAgingReportModel> buildASUAgingReportList(List<Document> documents, Map<String, Map<String, String>> invoicesMap) {

        if (CollectionUtils.isEmpty(documents)) {
            return new ArrayList<>();
        }

        return documents.stream()
                .map(document -> buildASUAgingReport(document, invoicesMap.get(document.getString(INVOICE_ID))))
                .collect(Collectors.toList());
    }

    public static ASUAgingReportModel buildASUAgingReport(Document document, Map<String, String> invoiceMap) {
        ASUAgingReportModel asuAgingReportModel = new ASUAgingReportModel();

        if(document.get(ENDORSEMENT_TYPE) == null){
            // fetched policy document
            document = document.get(UPDATED_POLICY_INFO, Document.class);
            asuAgingReportModel.setTransactionType(document.get(TRANSACTION_TYPE));
        }else{
            asuAgingReportModel.setTransactionType(ENDORSEMENT);
            // fetched policy document
            document = document.get(UPDATED_POLICY_INFO, Document.class);
        }

        asuAgingReportModel.setPolicyNumber(document.get(POLICY_NUMBER));
        // insuredInfo
        Map insuredInfo = document.get(INSURED_INFO, Map.class);
        asuAgingReportModel.setCustomerName(insuredInfo.get(FIRST_NAME));

        // brokerInfo
        Map brokerInfo = document.get(BROKER_INFO, Map.class);

        asuAgingReportModel.setBrokerName(brokerInfo.get(SENDER_NAME));
        asuAgingReportModel.setBrokerEmail(brokerInfo.get(SENDER_EMAIL));


        String brokerAddress = constructBrokerAddress((Map) brokerInfo.get(MAILING_ADDRESS));
        asuAgingReportModel.setMailingAddress(brokerAddress);

        asuAgingReportModel.setUnderwriter(document.get(UNDERWRITER_NAME));

        LocalDate todayDate = LocalDate.now();
        asuAgingReportModel.setRunDate(todayDate.format(DATE_FORMATTER));

        if (MapUtils.isNotEmpty(invoiceMap)) {
            asuAgingReportModel.setInvoiceNumber(invoiceMap.get(INVOICE_NUMBER));

            LocalDate billedDate = LocalDate.parse(invoiceMap.get(BILLED_DATE));

            String dueDateString = invoiceMap.get(DUE_DATE);

            processDueDate(asuAgingReportModel,dueDateString,invoiceMap,todayDate);

            asuAgingReportModel.setInvoiceDate(billedDate.format(DATE_FORMATTER));
        }

        //premiumDetails
        Map premiumDetails = document.get(PREMIUM_DETAILS, Map.class);

        Object totalWrittenPremium = premiumDetails.get(TOTAL_WRITTEN_PREMIUM);

        Double grossDue = null;

        if (ObjectUtils.isNotEmpty(totalWrittenPremium)) {
            grossDue = NumberParser.parseToDouble((String) totalWrittenPremium);
        }

        Object totalFees = premiumDetails.get(TOTAL_FEES);

        if (ObjectUtils.isNotEmpty(totalFees)) {
            grossDue = Objects.nonNull(grossDue) ? grossDue + NumberParser.parseToDouble((String) totalFees) : NumberParser.parseToDouble((String) totalFees);
        }

        Object triaPremium = premiumDetails.get(TRIA_PREMIUM);

        if (ObjectUtils.isNotEmpty(triaPremium)) {
            grossDue = Objects.nonNull(grossDue) ? grossDue + NumberParser.parseToDouble((String) triaPremium) : NumberParser.parseToDouble((String) triaPremium);
        }

        // premiumDetails -> commissionDetails
        Map commissionDetails = (Map) premiumDetails.get(COMMISSION_DETAILS);
        String commissionAmount = (String) commissionDetails.get(AMOUNT);

        Double accountingBalance;
        double parsedCommission = commissionAmount != null ? Double.parseDouble(commissionAmount) : 0.0;

        if (Objects.nonNull(grossDue)) {
            // The Gross Due is total written premium + tria premium + fees.
            asuAgingReportModel.setGrossDue(DOLLAR + numberFormat.format(NumberParser.parseToDouble(grossDue.toString())));

            // Total Written Premium + Tria premium + Fees - Commission Due to Broker = Accounting Balance.
            // Calculate the accounting balance based on the gross due amount.
            // If grossDue is positive or zero, subtract the commission amount.
            // If grossDue is negative, add the commission amount instead.
            accountingBalance = grossDue >= 0
                    ? grossDue - parsedCommission
                    : grossDue + parsedCommission;
        }else{
            accountingBalance = parsedCommission;
        }

        asuAgingReportModel.setAccountingBalance(DOLLAR + numberFormat.format(NumberParser.parseToDouble(accountingBalance.toString())));

        return asuAgingReportModel;
    }

    public static List<PayableCarrierReportModel> buildPayableCarrierReportList(List<Document> documents, Map<String, Map<String, String>> invoicesMap) {

        if (CollectionUtils.isEmpty(documents)) {
            return new ArrayList<>();
        }

        return documents.stream()
                .map(document -> buildPayableCarrierReport(document,
                        invoicesMap.get(document.getString(INVOICE_ID))))
                .collect(Collectors.toList());
    }

    public static List<InvoiceDetailReportModel> buildInvoiceDetailReportListFromPolicyEndorsement(List<Document> documents, Map<String, Map<String, String>> invoicesMap) {

        if (CollectionUtils.isEmpty(documents)) {
            return new ArrayList<>();
        }

        return documents.stream()
                .map(document -> buildInvoiceDetailReport(document.get(UPDATED_POLICY_INFO, Document.class),
                        invoicesMap.get(document.getString(INVOICE_ID))))
                .collect(Collectors.toList());


    }

    public static List<InvoiceDetailReportModel> buildInvoiceDetailReportListFromPolicy(List<Document> documents, Map<String, Map<String, String>> invoicesMap) {

        if (CollectionUtils.isEmpty(documents)) {
            return new ArrayList<>();
        }

        return documents.stream()
                .map(document -> buildInvoiceDetailReport(document,
                        invoicesMap.get(document.getString(INVOICE_ID))))
                .collect(Collectors.toList());
    }

    public static InvoiceDetailReportModel buildInvoiceDetailReport(Document policyDocument, Map<String, String> invoiceMap) {
        InvoiceDetailReportModel invoiceDetailReportModel = new InvoiceDetailReportModel();

        invoiceDetailReportModel.setPolicyNumber(policyDocument.get(POLICY_NUMBER));

        // insuredInfo
        Map insuredInfo = policyDocument.get(INSURED_INFO, Map.class);
        invoiceDetailReportModel.setInsuredName(insuredInfo.get(FIRST_NAME));

        if (MapUtils.isNotEmpty(invoiceMap)) {

            invoiceDetailReportModel.setInvoiceNumber(invoiceMap.get(INVOICE_NUMBER));

            String allocatedAmount = String.valueOf(invoiceMap.get(ALLOCATED_AMOUNT));

            if (StringUtils.isNotEmpty(allocatedAmount)) {
                invoiceDetailReportModel.setInvoiceTotalDue(DOLLAR + numberFormat.format(NumberParser.parseToDouble(allocatedAmount)));
            }

            LocalDate billedDate = LocalDate.parse(invoiceMap.get(BILLED_DATE));
            invoiceDetailReportModel.setInvoiceBilledDate(billedDate.format(DATE_FORMATTER));
        }

        //premiumDetails
        Map premiumDetails = policyDocument.get(PREMIUM_DETAILS, Map.class);

        Object totalWrittenPremium = premiumDetails.get(TOTAL_WRITTEN_PREMIUM);

        Double premium = null;

        if (ObjectUtils.isNotEmpty(totalWrittenPremium)) {
            premium = Double.parseDouble((String) totalWrittenPremium);
        }

        if (Objects.nonNull(premium)) {
            // The premium is total written premium + tria Premium.
            invoiceDetailReportModel.setPremium(DOLLAR + numberFormat.format(NumberParser.parseToDouble(premium.toString())));
        }

        Object totalFees = premiumDetails.get(TOTAL_FEES);

        if (ObjectUtils.isNotEmpty(totalFees)) {
            invoiceDetailReportModel.setTotalFees(formatDollarValue(totalFees));
        }

        // premiumDetails -> commissionDetails
        Map commissionDetails = (Map) premiumDetails.get(COMMISSION_DETAILS);

        Object commissionAmount = commissionDetails.get(AMOUNT);

        if (ObjectUtils.isNotEmpty(commissionAmount)) {
            invoiceDetailReportModel.setCommission(formatDollarValue(commissionAmount));
        }

        return invoiceDetailReportModel;
    }

    public static PayableCarrierReportModel buildPayableCarrierReport(Document endorsementDocument, Map<String, String> invoiceMap) {
        PayableCarrierReportModel payableCarrierReportModel = new PayableCarrierReportModel();

        // policy doc
        Document policyDocument = endorsementDocument.get(UPDATED_POLICY_INFO, Document.class);

        payableCarrierReportModel.setPolicyNumber(policyDocument.get(POLICY_NUMBER));

        // insuredInfo
        Map insuredInfo = policyDocument.get(INSURED_INFO, Map.class);
        payableCarrierReportModel.setInsuredName(insuredInfo.get(FIRST_NAME));

        payableCarrierReportModel.setCarrier(policyDocument.get(CARRIER, String.class));


        if (MapUtils.isNotEmpty(invoiceMap)) {

            payableCarrierReportModel.setInvoiceNumber(invoiceMap.get(INVOICE_NUMBER));

            String allocatedAmount = String.valueOf(invoiceMap.get(ALLOCATED_AMOUNT));

            if (StringUtils.isNotEmpty(allocatedAmount)) {
                payableCarrierReportModel.setInvoiceTotalDue(DOLLAR + numberFormat.format(NumberParser.parseToDouble(allocatedAmount)));
            }

            LocalDate billedDate = LocalDate.parse(invoiceMap.get(BILLED_DATE));
            payableCarrierReportModel.setInvoiceBilledDate(billedDate.format(DATE_FORMATTER));
        }

        //premiumDetails
        Map premiumDetails = policyDocument.get(PREMIUM_DETAILS, Map.class);

        Object totalWrittenPremium = premiumDetails.get(TOTAL_WRITTEN_PREMIUM);

        Double premium = null;

        if (ObjectUtils.isNotEmpty(totalWrittenPremium)) {
            premium = Double.parseDouble((String) totalWrittenPremium);
        }

        if (Objects.nonNull(premium)) {
            // The premium is total written premium
            payableCarrierReportModel.setPremium(DOLLAR + numberFormat.format(NumberParser.parseToDouble(premium.toString())));
        }

        Object totalFees = premiumDetails.get(TOTAL_FEES);

        if (ObjectUtils.isNotEmpty(totalFees)) {
            payableCarrierReportModel.setTotalFees(formatDollarValue(totalFees));
        }

        // premiumDetails -> commissionDetails
        Map commissionDetails = (Map) premiumDetails.get(COMMISSION_DETAILS);

        Object commissionPercentage = commissionDetails.get(PERCENTAGE_AS_STRING);

        if (ObjectUtils.isNotEmpty(commissionPercentage)) {
            payableCarrierReportModel.setCommissionPercentage(commissionPercentage + PERCENTAGE);
        }

        Object commissionAmount = commissionDetails.get(AMOUNT);

        if (ObjectUtils.isNotEmpty(commissionAmount)) {
            payableCarrierReportModel.setCommission(DOLLAR + numberFormat.format(NumberParser.parseToDouble(commissionAmount.toString())));
        }

        return payableCarrierReportModel;
    }


    public static List<AccountReceivableAgingReportModel> buildAccountReceivableAgingReportList(List<Document> documents) {

        if (CollectionUtils.isEmpty(documents)) {
            return new ArrayList<>();
        }

        return documents.stream()
                .map(document -> buildAccountReceivableAgingReport(document))
                .collect(Collectors.toList());
    }

    public static AccountReceivableAgingReportModel buildAccountReceivableAgingReport(Document document) {
        AccountReceivableAgingReportModel accountReceivableAgingReportModel = new AccountReceivableAgingReportModel();

        accountReceivableAgingReportModel.setPolicyNumber(document.get(POLICY_NUMBER));
        // insuredInfo
        Map insuredInfo = document.get(INSURED_INFO, Map.class);
        accountReceivableAgingReportModel.setCustomerName(insuredInfo.get(FIRST_NAME));

        LocalDate billedDate = LocalDate.parse(document.get(INVOICE_BILLED_DATE, String.class));
        LocalDate dueDate = LocalDate.parse(document.get(INVOICE_DUE_DATE, String.class));

        accountReceivableAgingReportModel.setInvoiceNumber(document.get(INVOICE_NUMBER));

        Object allocatedAmount = document.get(INVOICE_ALLOCATED_AMOUNT, Object.class);
        String allocationAmountStr = null;

        if (Objects.nonNull(allocatedAmount)) {
            allocationAmountStr = DOLLAR + numberFormat.format(allocatedAmount);
        }

        accountReceivableAgingReportModel.setInvoiceDate(billedDate.format(DATE_FORMATTER));

        LocalDate todayDate = LocalDate.now();

        long dayPastDue = ChronoUnit.DAYS.between(dueDate, todayDate);

        if(dayPastDue < 30){
            accountReceivableAgingReportModel.setCurrent(allocationAmountStr);
        }else if (dayPastDue <= 59){
            accountReceivableAgingReportModel.setThirtyDayNotice(allocationAmountStr);
        }else if(dayPastDue <= 89){
            accountReceivableAgingReportModel.setSixtyDayNotice(allocationAmountStr);
        }else if(dayPastDue <= 119){
            accountReceivableAgingReportModel.setNinetyDayNotice(allocationAmountStr);
        }else {
            accountReceivableAgingReportModel.setHundredTwentyDays(allocationAmountStr);
        }

        accountReceivableAgingReportModel.setBalance(allocationAmountStr);

        return accountReceivableAgingReportModel;
    }

    public static String convertToMillions(String input) {
        if (input == null || input.isEmpty()) {
            log.error("Input is null or empty. Returning original input.");
            return "N/A";
        }

        try {
            log.info("convertToMillions input: {}", input);

            // Remove the currency symbol and commas
            String sanitizedInput = input.replaceAll("[$,]", "");

            // Check if the sanitized input is a valid double
            if (!isValidDouble(sanitizedInput)) {
                log.error("Invalid numeric input format for '{}'. Returning original input.", input);
                return "N/A";
            }

            // Parse the cleaned string to a double
            double numericValue = Double.parseDouble(sanitizedInput);

            // Convert the value to millions
            double valueInMillions = numericValue / 1_000_000;

            // Return the result as a string without decimals
            return String.valueOf((int) valueInMillions);
        } catch (NumberFormatException e) {
            log.error("Unexpected error parsing input '{}'. Returning original input.", input, e);
            return "N/A"; // Return the original input in case of an error
        }
    }

    // Utility method to check if a string is a valid double
    private static boolean isValidDouble(String input) {
        try {
            Double.parseDouble(input);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    // changes
    private static void appendLimit(Object limitValue, StringBuilder policyLimits) {
        if (Objects.nonNull(limitValue) && !Objects.equals(String.valueOf(limitValue), ZERO_AS_STRING)) {
            String limitInMillions = convertToMillions(String.valueOf(limitValue));
            policyLimits.append(limitInMillions).append(SLASH);
        }
    }
    private static void setRetentionDetails(String retentionType, BordereauReportModel bordereauReportModel, Object deductibleAmount, Object sirAmount) {

        if (retentionType == null) {
            // Handle the null case explicitly
            bordereauReportModel.setDedOrSirValue(NOT_APPLICABLE);
            bordereauReportModel.setRetentionType(null); // Set to null or any default value as needed
            return;
        }

        switch (retentionType) {
            case DEDUCTIBLE:
                bordereauReportModel.setDedOrSirValue(deductibleAmount);
                bordereauReportModel.setRetentionType("D");
                break;
            case SELF_INSURED_RETENTION:
                bordereauReportModel.setDedOrSirValue(sirAmount);
                bordereauReportModel.setRetentionType("S");
                break;
            default:
                bordereauReportModel.setDedOrSirValue(NOT_APPLICABLE);
                break;
        }
    }
    private static Double calculateAndSetPremium(Object updatedPremiumObject, String fieldName, Consumer<String> setter) {
        if (ObjectUtils.isNotEmpty(updatedPremiumObject)) {
            Double updatedPremium = NumberParser.parseToDouble((String) updatedPremiumObject);
            setter.accept(DOLLAR + numberFormat.format(updatedPremium));
            log.info("calculateAndSetPremium for: {}", fieldName);
            return updatedPremium;
        }
        return 0.0;
    }
    private static String getStatus(Document document) {
        String status = document.getString(STATE);
        if (ENDORSEMENT.equalsIgnoreCase(status)) {
            return document.getString(LAST_POLICY_STATE);
        }
        return status;
    }
    private static void processPremiumDetail(Map premiumDetails, String key, Consumer<String> setter) {
        if(premiumDetails != null)
        {
            Object value = premiumDetails.get(key);
            if (ObjectUtils.isNotEmpty(value)) {
                setter.accept(formatDollarValue(value));
            }
        }
    }

    private static Double getParsedValue(Map premiumDetails, String key) {
        Object value = premiumDetails.get(key);
        return ObjectUtils.isNotEmpty(value) ? NumberParser.parseToDouble((String) value) : 0.0;
    }
    private static void processPolicyBoundDate(String policyBoundDateObject, BordereauReportModel bordereauReportModel) {
        if (StringUtils.isNotEmpty(policyBoundDateObject)) {
            LocalDate policyBoundDate = LocalDate.parse(policyBoundDateObject);
            String accountingMonth = policyBoundDate.getMonth().getDisplayName(TextStyle.SHORT, Locale.getDefault()) + HYPHEN + policyBoundDate.getYear();
            bordereauReportModel.setAccountingMonth(accountingMonth);

            LocalDate premiumDueDate = policyBoundDate.plusDays(30);
            bordereauReportModel.setPremiumDueDate(premiumDueDate.format(DATE_FORMATTER));
        }
    }

    private static void processCommissionDetails(Map premiumDetails, BordereauReportModel bordereauReportModel) {
        Map commissionDetails = (Map) premiumDetails.get(COMMISSION_DETAILS);

        Object commissionPercentage = commissionDetails.get(PERCENTAGE_AS_STRING);
        if (ObjectUtils.isNotEmpty(commissionPercentage)) {
            bordereauReportModel.setCommissionPercentage(commissionPercentage + PERCENTAGE);
        }

        Object commissionAmount = commissionDetails.get(AMOUNT);
        if (ObjectUtils.isNotEmpty(commissionAmount)) {
            bordereauReportModel.setCommission(formatDollarValue(commissionAmount));
        }
    }

    private static void processCommissionDetailForCancelEndorsement(BordereauReportModel bordereauReportModel, Document policyEndorsementDocument) {
        if ("CANCEL".equals(policyEndorsementDocument.getString(ENDORSEMENT_TYPE)) || "CANCELLATION".equals(policyEndorsementDocument.getString(ENDORSEMENT_TYPE))) {
            String commissionAmount = (String) bordereauReportModel.getCommission();
            if (commissionAmount != null && !commissionAmount.startsWith("-")) {
                bordereauReportModel.setCommission("-" + commissionAmount);
            }
        }
    }
    private static void processEndorsementCreatedDate(Object endorsementCreatedDateObject, BordereauReportModel bordereauReportModel) {
        if (Objects.nonNull(endorsementCreatedDateObject)) {
            OffsetDateTime offsetDateTime = ((Date) endorsementCreatedDateObject).toInstant().atOffset(ZoneOffset.UTC);
            LocalDate endorsementCreatedDate = offsetDateTime.toLocalDate();
            LocalDate premiumDueDate = endorsementCreatedDate.plusDays(30);
            bordereauReportModel.setPremiumDueDate(premiumDueDate.format(DATE_FORMATTER));
        }
    }

    private static void processPolicyBoundDateWithoutPremiumDate(String policyBoundDateObject, BordereauReportModel bordereauReportModel) {
        if (StringUtils.isNotEmpty(policyBoundDateObject)) {
            LocalDate policyBoundDate = LocalDate.parse(policyBoundDateObject);
            String accountingMonth = policyBoundDate.getMonth().getDisplayName(TextStyle.SHORT, Locale.getDefault())
                    + HYPHEN + policyBoundDate.getYear();
            bordereauReportModel.setAccountingMonth(accountingMonth);
        }
    }
    private static void updateCancelEndorsement(Document policyEndorsementDocument, Double updatedTotalWrittenPremium,
                                                Double updatedTriaPremium, Double updatedTotalFees, BordereauReportModel bordereauReportModel) {
        if(Objects.isNull(policyEndorsementDocument.getString(ENDORSEMENT_TYPE))){
            bordereauReportModel.setStatus(ISSUED);
        } else if(CANCEL_ENDORSEMENT_STATUSES.contains(policyEndorsementDocument.getString(ENDORSEMENT_TYPE))) {
            bordereauReportModel.setStatus(policyEndorsementDocument.getString(ENDORSEMENT_TYPE));
            bordereauReportModel.setTotalWrittenPremium(DOLLAR + numberFormat.format(updatedTotalWrittenPremium));
            bordereauReportModel.setTriaPremium(DOLLAR + numberFormat.format(updatedTriaPremium));
            bordereauReportModel.setFeeAmount(DOLLAR + numberFormat.format(updatedTotalFees));
            Double totalPremium = updatedTotalWrittenPremium + updatedTriaPremium;
            bordereauReportModel.setTotalPremium(DOLLAR + numberFormat.format(totalPremium));
        } else {
            bordereauReportModel.setStatus(ENDORSEMENT);
        }
    }

    private static void handleCommissionDetails(Document policyEndorsementDocument, Map updatedPremiumDetails,
                                                BordereauReportModel bordereauReportModel) {
        if(NON_MONETARY_ENDORSEMENT_STATUS.equals(policyEndorsementDocument.getString(ENDORSEMENT_TYPE))) {
            bordereauReportModel.setCommissionPercentage(ZERO_AS_STRING + PERCENTAGE);
            bordereauReportModel.setCommission(DOLLAR + numberFormat.format(NumberParser.parseToDouble(ZERO_AS_STRING)));
        } else {
            processCommissionDetails(updatedPremiumDetails, bordereauReportModel);
            processCommissionDetailForCancelEndorsement(bordereauReportModel, policyEndorsementDocument);
        }
    }
    private static Document fetchPayload(Document boundPolicyDocumentPayload) {
        Document payload = boundPolicyDocumentPayload.get("payload", Document.class);
        if (payload == null) {
            log.error("Payload is null for document: {}", boundPolicyDocumentPayload);
        }
        return payload;
    }

    private static Map fetchPremiumDetails(Document payload, Document document, boolean isPolicyWithEndorsement) {
        if (isPolicyWithEndorsement) {
            return payload != null ? payload.get(PREMIUM_DETAILS, Map.class) : null;
        }
        return document.get(PREMIUM_DETAILS, Map.class);
    }
    private static String determineRenewalIdentifier(String transactionType) {
        return StringUtils.equalsIgnoreCase(transactionType, RENEWAL) ? "Yes" : "No";
    }
    private static void setExposureLimits(AllDataReportModel allDataReportModel, Document exposureDetails, Document document) {
        if (exposureDetails != null) {
            String productState = document.get(PRODUCT_STATE, String.class);

            if (StringUtils.isNotEmpty(productState) && productState.equalsIgnoreCase(EXCESS)) {
                allDataReportModel.setEachOccurrenceLimit(formatDollarValue(exposureDetails.get(EACH_OCCURRENCE_LIMIT_EXCESS)));
                allDataReportModel.setProductsCompletedOpsAggregate(formatDollarValue(exposureDetails.get(PRODUCTS_COMPLETED_OPERATIONS_AGGREGATE)));
                allDataReportModel.setOtherAggregateLimit(formatDollarValue(exposureDetails.get(OTHER_AGGREGATE_LIMIT)));
                allDataReportModel.setPolicyAggregateLimit(formatDollarValue(exposureDetails.get(POLICY_AGGREGATE_LIMIT)));

            } else {
                allDataReportModel.setEachOccurrenceLimit(formatDollarValue(exposureDetails.get(EACH_OCCURRENCE_LIMIT_PRIMARY)));
                allDataReportModel.setGeneralAggregateLimit(formatDollarValue(exposureDetails.get(GENERAL_AGGREGATE_LIMIT)));
                allDataReportModel.setProductsCompletedOpsAggregateLimit(formatDollarValue(exposureDetails.get(PRODUCTS_COMPLETED_OPERATIONS_AGGREGATE_LIMIT)));
                allDataReportModel.setPersonalAdvertisingInjuryLimit(formatDollarValue(exposureDetails.get(PERSONAL_ADVERTISING_INJURY_LIMIT)));
                allDataReportModel.setDamageToPremisesRented(formatDollarValue(exposureDetails.get(DAMAGE_TO_PREMISES_RENTED)));
                allDataReportModel.setMedicalPaymentsLimit(formatDollarValue(exposureDetails.get(MEDICAL_PAYMENTS_LIMIT)));
            }

            allDataReportModel.setGeneralAggregateReinstatements(exposureDetails.get("numberOfGeneralAggregateReinstatement"));
        }
    }
    private static String formatDollarValue(Object value) {
        if (value instanceof String) {
            double parsedValue = NumberParser.parseToDouble((String) value);
            return DOLLAR + numberFormat.format(parsedValue);
        } else if (value instanceof Double) {
            return DOLLAR + numberFormat.format(value);
        }
        return DOLLAR + "0.00";
    }
    private static void setDeductibleDetails(AllDataReportModel allDataReportModel, Document deductibleDetails) {
        if (deductibleDetails != null) {
            allDataReportModel.setRetentionType(deductibleDetails.getString("retentionType"));
            allDataReportModel.setDeductibleAmount(formatDollarValue(deductibleDetails.get("deductibleAmount")));
            allDataReportModel.setSirAmount(formatDollarValue(deductibleDetails.get("sirAmount")));
        }
    }
    private static String constructBrokerAddress(Map mailingAddress) {
        StringBuilder brokerAddressBuilder = new StringBuilder();

        if (ObjectUtils.isNotEmpty(mailingAddress.get(STREET))) {
            brokerAddressBuilder.append(mailingAddress.get(STREET).toString().trim());
        }

        if (ObjectUtils.isNotEmpty(mailingAddress.get(CITY))) {
            brokerAddressBuilder.append(mailingAddress.get(CITY).toString().trim()).append(", ");
        }

        if (ObjectUtils.isNotEmpty(mailingAddress.get(STATE))) {
            brokerAddressBuilder.append(mailingAddress.get(STATE).toString().trim()).append(", ");
        }

        if (ObjectUtils.isNotEmpty(mailingAddress.get(ZIP_CODE))) {
            brokerAddressBuilder.append(mailingAddress.get(ZIP_CODE).toString().trim());
        }

        return brokerAddressBuilder.toString();
    }
    private static void processDueDate(ASUAgingReportModel asuAgingReportModel, String dueDateString, Map<String, String> invoiceMap, LocalDate todayDate) {
        if(!StringUtils.equalsIgnoreCase(dueDateString, NOT_APPLICABLE)){
            LocalDate dueDate = LocalDate.parse(invoiceMap.get(DUE_DATE));
            asuAgingReportModel.setDueDate(dueDate.format(DATE_FORMATTER));

            long dayPastDue = ChronoUnit.DAYS.between(dueDate, todayDate);

            if(dayPastDue < 1){
                asuAgingReportModel.setDayPastDue(NOT_APPLICABLE);
            }else{
                asuAgingReportModel.setDayPastDue(dayPastDue);
            }

            asuAgingReportModel.setThirtyOneDayNotice(dueDate.plusDays(1).format(DATE_FORMATTER));
            asuAgingReportModel.setFortyDayNotice(dueDate.plusDays(10).format(DATE_FORMATTER));
            asuAgingReportModel.setFiftyFourDayNotice(dueDate.plusDays(24).format(DATE_FORMATTER));
        }else{
            asuAgingReportModel.setDueDate(NOT_APPLICABLE);
            asuAgingReportModel.setDayPastDue(NOT_APPLICABLE);
            asuAgingReportModel.setThirtyOneDayNotice(NOT_APPLICABLE);
            asuAgingReportModel.setFortyDayNotice(NOT_APPLICABLE);
            asuAgingReportModel.setFiftyFourDayNotice(NOT_APPLICABLE);
        }
    }
}
