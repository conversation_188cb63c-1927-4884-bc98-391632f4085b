package com.concirrus.reporting.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.Instant;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class LoggingUtils {
    public static final String METHOD_ENTER = "Entering into : ";
    public static final String METHOD_EXIT = "Exiting from : ";
    public static final String METHOD_PARAMS = " Method params: ";
    private static final long ONE_MB = 1024 * 1024l;
    private static final String USED_MEMORY = "Used Memory   :  ";
    private static final String FREE_MEMORY = "Free Memory   :  ";
    private static final String TOTAL_MEMORY = "Total Memory  :  ";
    private static final String MAX_MEMORY = "Max Memory    :  ";
    private static final String MB = " MB";
    private static final String LOG_MSG = "{}{}{}";


    /**
     * Method to log each calls entry in method with the list of arguments
     *
     * @param arguments
     * @return
     */
    public static String logMethodEntry(Object... arguments) {
        StringBuilder sb = new StringBuilder();
        sb.append(METHOD_ENTER);
        sb.append(getMethodName());
        if (arguments.length > 0) {
            sb.append(METHOD_PARAMS);
        }
        int count = 1;
        for (Object obj : arguments) {
            if (obj == null) {
                obj = "NULL";
            }
            sb.append("Arg" + count + ": " + obj.toString() + " ");
            count++;
        }
        return sb.toString();
    }

    public static String getMethodName() {
        return Thread.currentThread().getStackTrace()[3].getMethodName();
    }

    public static String logMethodExit() {
        return METHOD_EXIT + getMethodName();
    }

    public static String logMethodExitWithDuration(Instant start) {
        return new StringBuilder()
                .append(METHOD_EXIT)
                .append(getMethodName())
                .append(" in ")
                .append(Duration.between(start, Instant.now()))
                .append(" ms.")
                .toString();
    }

    public static String logMethodEntryWithMemory() {
        return new StringBuilder()
                .append(METHOD_ENTER)
                .append(getMethodName())
                .append(System.lineSeparator())
                .append(memoryUsage())
                .append(System.lineSeparator())
                .append(freeMemoryUsage())
                .append(System.lineSeparator())
                .append(totalMemoryUsage())
                .append(System.lineSeparator())
                .append(maxMemoryUsage())
                .toString();
    }

    public static String logMethodExitWithMemory() {
        return new StringBuilder()
                .append(METHOD_EXIT)
                .append(getMethodName())
                .append(System.lineSeparator())
                .append(memoryUsage())
                .append(System.lineSeparator())
                .append(freeMemoryUsage())
                .append(System.lineSeparator())
                .append(totalMemoryUsage())
                .append(System.lineSeparator())
                .append(maxMemoryUsage())
                .toString();
    }

    public static String logMethodExitWithMemoryAndDuration(Instant start) {
        return new StringBuilder()
                .append(METHOD_EXIT)
                .append(getMethodName())
                .append(" in ")
                .append(Duration.between(start, Instant.now()))
                .append(" ms.")
                .append(System.lineSeparator())
                .append(memoryUsage())
                .append(System.lineSeparator())
                .append(freeMemoryUsage())
                .append(System.lineSeparator())
                .append(totalMemoryUsage())
                .append(System.lineSeparator())
                .append(maxMemoryUsage())
                .toString();
    }

    public static String memoryUsage() {
        return new StringBuilder()
                .append(USED_MEMORY)
                .append((Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()) / ONE_MB)
                .append(MB)
                .toString();
    }

    public static String freeMemoryUsage() {
        return new StringBuilder()
                .append(FREE_MEMORY)
                .append(Runtime.getRuntime().freeMemory() / ONE_MB)
                .append(MB)
                .toString();
    }

    public static String totalMemoryUsage() {
        return new StringBuilder()
                .append(TOTAL_MEMORY)
                .append(Runtime.getRuntime().totalMemory() / ONE_MB)
                .append(MB)
                .toString();
    }

    public static String maxMemoryUsage() {
        return new StringBuilder()
                .append(MAX_MEMORY)
                .append(Runtime.getRuntime().maxMemory() / ONE_MB)
                .append(MB)
                .toString();
    }

    public static void printMemoryUsage() {
        log.info(LOG_MSG, USED_MEMORY, (Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()) / ONE_MB, MB);
        log.info(LOG_MSG, FREE_MEMORY, Runtime.getRuntime().freeMemory() / ONE_MB, MB);
        log.info(LOG_MSG, TOTAL_MEMORY, +Runtime.getRuntime().totalMemory() / ONE_MB, MB);
        log.info(LOG_MSG, MAX_MEMORY, +Runtime.getRuntime().maxMemory() / ONE_MB, MB);
    }


}
