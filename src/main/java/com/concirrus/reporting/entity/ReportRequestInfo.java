package com.concirrus.reporting.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "report_request")
public class ReportRequestInfo {
    @Id
    private String id;
    private String clientId;
    private String fileName;
    private String reportType;
    private LocalDate startDate;
    private LocalDate endDate;
    private String status;
    private String fileDownloadLink;

    private String errorMessage;

    private LocalDate runDate;
    private String createdBy;
    private String createdByUserName;
    private LocalDateTime createdAt;
}
