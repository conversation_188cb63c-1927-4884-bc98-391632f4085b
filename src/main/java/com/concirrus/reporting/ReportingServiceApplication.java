package com.concirrus.reporting;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@EnableAsync
public class ReportingServiceApplication {

	private static final Logger logger = LoggerFactory.getLogger(ReportingServiceApplication.class);

	public static void main(String[] args) {
		logger.info("***************** Starting Reporting Service *******************");

		// Workaround for font loading issues with Apache POI in Docker containers.
		// Prevents errors when system fonts are unavailable by ignoring missing font system.
		// Reference: https://stackoverflow.com/questions/76561185/unable-to-use-sxssfworkbook-apache-poi-inside-docker/77898047
		System.setProperty("org.apache.poi.ss.ignoreMissingFontSystem", "true");

		SpringApplication.run(ReportingServiceApplication.class, args);
		logger.info("***************** Reporting Service Started *******************");
	}

}
