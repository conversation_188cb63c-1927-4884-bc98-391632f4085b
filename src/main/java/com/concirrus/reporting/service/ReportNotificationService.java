package com.concirrus.reporting.service;

import com.concirrus.reporting.dto.EmailAlertEvent;
import com.concirrus.reporting.dto.SubmissionRequestDTO;
import com.concirrus.reporting.enums.AlertMedium;
import com.concirrus.reporting.utils.LoggingUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.concirrus.reporting.constant.ReportingConstant.*;

@Service
@Slf4j
public class ReportNotificationService {
    
    private final IEventMessageService eventMessageService;
    private final ObjectMapper objectMapper;
    private final String submissionQueueUrl;
    
    @Autowired
    public ReportNotificationService(IEventMessageService eventMessageService, 
                                   ObjectMapper objectMapper,
                                   @Value("${cloud.queue.out.submission}") String submissionQueueUrl) {
        this.eventMessageService = eventMessageService;
        this.objectMapper = objectMapper;
        this.submissionQueueUrl = submissionQueueUrl;
    }

    public void sendReportGenerationEmail(String fileName, String clientId, String reportBase64Content, SubmissionRequestDTO submissionRequestDTO) {
        log.info(LoggingUtils.logMethodEntry(clientId));
        try {
            if(submissionRequestDTO.isSubmissionStatus()) {
                eventMessageService.publishMessage(submissionQueueUrl, generateReportEmailPayload(fileName, clientId, reportBase64Content, submissionRequestDTO, WHITESPACE_AVIATION_REPORT));
            } else {
                eventMessageService.publishMessage(submissionQueueUrl, generateReportEmailPayload(fileName, clientId, reportBase64Content, submissionRequestDTO, WHITESPACE_AVIATION_FAILED_REPORT));
            }
        } catch (JsonProcessingException e) {
            log.error("Error while generating report email payload", e);
        }
        log.info(LoggingUtils.logMethodExit());
    }

    private String generateReportEmailPayload(String fileName, String clientId, String reportBase64Content, SubmissionRequestDTO submissionRequestDTO, String templateId) throws JsonProcessingException {
        EmailAlertEvent emailAlertEvent = new EmailAlertEvent();
        emailAlertEvent.setClientId(clientId);
        emailAlertEvent.setMedium(AlertMedium.EMAIL);
        emailAlertEvent.setReceivers(List.of(submissionRequestDTO.getEmail()));
        emailAlertEvent.setTemplateId(templateId);
        emailAlertEvent.setLob(AVIATION_LOB);

        // Create attachments map
        Map<String, String> attachments = Map.of(fileName, reportBase64Content);
        emailAlertEvent.setAttachments(attachments);

        // Create template args as JsonNode
        ObjectNode templateArgs = objectMapper.createObjectNode();
        templateArgs.put(INSURED_NAME, submissionRequestDTO.getSubmissionBody().getBody().getRiskDetails().getInsured());
        templateArgs.put(SUBMITTED_BY, submissionRequestDTO.getCreatedBy());
        templateArgs.put(SUBMISSION_DATE, submissionRequestDTO.getReceivedDate());
        templateArgs.put(TIME_TO_GENERATE_JSON, submissionRequestDTO.getProcessingTime());
        templateArgs.put(TIME_TO_COMPLETE_PROCESSING, submissionRequestDTO.getCompleteProcessingTime());
        templateArgs.put(COMMENT, submissionRequestDTO.getComment());
        emailAlertEvent.setTemplateArgs(templateArgs);

        // Convert to JSON string
        return objectMapper.writeValueAsString(emailAlertEvent);
    }
}