package com.concirrus.reporting.service.impl;


import com.concirrus.reporting.annotation.XlsxSheet;
import com.concirrus.reporting.dal.AuditDAL;
import com.concirrus.reporting.dal.EndorsementDAL;
import com.concirrus.reporting.dal.PolicyDAL;
import com.concirrus.reporting.dal.ReportingDAL;
import com.concirrus.reporting.dto.PolicySubmissionAuditDTO;
import com.concirrus.reporting.dto.ReportRequestDTO;
import com.concirrus.reporting.dto.ReportRequestInfoDTO;
import com.concirrus.reporting.dto.SubmissionRequestDTO;
import com.concirrus.reporting.entity.ReportRequestInfo;
import com.concirrus.reporting.enums.ReportingRequestStatus;
import com.concirrus.reporting.enums.ReportingType;
import com.concirrus.reporting.event.ReportGenerationEvent;
import com.concirrus.reporting.event.SubmissionExtractionEvent;
import com.concirrus.reporting.helper.StorageService;
import com.concirrus.reporting.model.XlsxField;
import com.concirrus.reporting.model.exception.TooEarlyRequestException;
import com.concirrus.reporting.model.report.*;
import com.concirrus.reporting.sal.AuditSAL;
import com.concirrus.reporting.sal.BrokerageHubSAL;
import com.concirrus.reporting.sal.InvoiceSAL;
import com.concirrus.reporting.sal.UserSAL;
import com.concirrus.reporting.service.ReportingService;
import com.concirrus.reporting.service.ReportNotificationService;
import com.concirrus.reporting.utils.AppliedUtilityMapper;
import com.concirrus.reporting.utils.LoggingUtils;
import com.concirrus.reporting.writer.XlsxWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.Base64;

import static com.concirrus.reporting.constant.DBConstant.*;
import static com.concirrus.reporting.constant.ReportConstant.BOUND;
import static com.concirrus.reporting.constant.ReportConstant.POLICY_BOUND_DATE;
import static com.concirrus.reporting.enums.ReportingType.AVIATION_BORDEREAU;
import static com.concirrus.reporting.enums.ReportingType.AVIATION_SUBMISSION;
import static com.concirrus.reporting.utils.UtilityMapper.*;

@Service
@Slf4j
public class ReportingServiceImpl implements ReportingService {

    private final PolicyDAL policyDAL;
    private final ReportingDAL reportingDAL;

    private final EndorsementDAL endorsementDAL;

    private final AuditDAL auditDAL;
    private final InvoiceSAL invoiceSAL;
    private final BrokerageHubSAL brokerageHubSAL;
    private final AuditSAL auditSAL;
    private final UserSAL userSAL;
    private final XlsxWriter xlsxWriter;
    private final StorageService storageService;
    private final String bucketName;
    private final int reportLinkExpirationInDays;

    private final int lastDaysOfReport;
    private final ApplicationEventPublisher eventPublisher;
    private final ReportNotificationService reportNotificationService;

    private static final List<String> BOUND_ENDORSEMENT_STATUSES = List.of("PROCESSING", "BOUND");

    private static final Integer ZERO = 0;
    private static final Integer HUNDRED = 100;

    @Autowired
    public ReportingServiceImpl(PolicyDAL policyDAL, ReportingDAL reportingDAL, EndorsementDAL endorsementDAL, AuditDAL auditDAL, InvoiceSAL invoiceSAL, BrokerageHubSAL brokerageHubSAL, AuditSAL auditSAL, UserSAL userSAL, XlsxWriter xlsxWriter, ApplicationEventPublisher eventPublisher, StorageService storageService, String bucketName, @Value("${report.link.expiration-days}") int reportLinkExpirationInDays, @Value("${last-days-report}") int lastDaysOfReport, ReportNotificationService reportNotificationService) {
        this.policyDAL = policyDAL;
        this.reportingDAL = reportingDAL;
        this.endorsementDAL = endorsementDAL;
        this.auditDAL = auditDAL;
        this.invoiceSAL = invoiceSAL;
        this.brokerageHubSAL = brokerageHubSAL;
        this.auditSAL = auditSAL;
        this.userSAL = userSAL;
        this.xlsxWriter = xlsxWriter;
        this.eventPublisher = eventPublisher;
        this.storageService = storageService;
        this.bucketName = bucketName;
        this.reportLinkExpirationInDays = reportLinkExpirationInDays;
        this.lastDaysOfReport = lastDaysOfReport;
        this.reportNotificationService = reportNotificationService;
    }


    /**
     * Creates a report request.
     * publish an event to start async report generation process
     *
     * @param reportRequestDTO The data transfer object containing report request details.
     * @param userId           The user ID associated with the report request.
     * @param clientId         The client ID associated with the report request.
     * @return The ID of the created report request.
     */
    @Override
    public String createReportRequest(ReportRequestDTO reportRequestDTO, String userId, String clientId) {
        log.info(LoggingUtils.logMethodEntry(reportRequestDTO, clientId));

        // sal call to access management service
        List<Map<String, Object>> userInfoList = userSAL.getUserInfoByIds(List.of(userId), clientId);
        Map<String, Object> userInfo = userInfoList.get(0);
        String firstName = (String) userInfo.get("firstName");
        String lastName = (String) userInfo.get("lastName");
        String userName = firstName + " " + lastName;

        // create report request
        ReportRequestInfo reportRequestInfo = createReportRequest(userId, userName, clientId);

        //set file name
        String fileName = generateFileNameByReportType(reportRequestDTO, reportRequestInfo.getId());
        reportRequestInfo.setFileName(fileName);

        //set report type
        reportRequestInfo.setReportType(reportRequestDTO.getReportingType().name());

        //set fromDate, toDate and runDate
        reportRequestInfo.setStartDate(reportRequestDTO.getStartDate());
        reportRequestInfo.setEndDate(reportRequestDTO.getEndDate());
        reportRequestInfo.setRunDate(LocalDate.now());

        reportRequestInfo = reportingDAL.updateReportRequest(reportRequestInfo.getId(), clientId, reportRequestInfo);

        // publish event (it will process asynchronously)
        eventPublisher.publishEvent(new ReportGenerationEvent(this, reportRequestDTO.getStartDate(), reportRequestDTO.getEndDate(), reportRequestDTO.getReportingType(), clientId, reportRequestInfo));

        log.info(LoggingUtils.logMethodExit());
        return reportRequestInfo.getId();
    }

    /**
     * Generates and uploads a report based on the specified parameters.
     *
     * @param startDate         The start date for the report.
     * @param endDate           The end date for the report.
     * @param reportingType     The type of report to generate.
     * @param clientId          The ID of the client for whom the report is generated.
     * @param reportRequestInfo Information about the report request.
     */
    public void reportGeneration(LocalDate startDate, LocalDate endDate, ReportingType reportingType, String clientId, ReportRequestInfo reportRequestInfo, String lob) {
        log.info(LoggingUtils.logMethodEntry(startDate, endDate, reportingType, reportRequestInfo, clientId));

        try {
            // Handle APPLIED_AVIATION_BORDEREAU - Stream upload
            if (List.of(AVIATION_BORDEREAU, AVIATION_SUBMISSION).contains(reportingType)) {
                generateAndUploadData(reportingType, reportRequestInfo.getFileName(), startDate, endDate, clientId);
            }else{
                // generate report file data
                byte[] fileData = getFileDataOfRequestedReport(reportingType, startDate, endDate, clientId);

                // upload it to Storage
                storageService.storeObject(bucketName, reportRequestInfo.getFileName(), fileData);
            }

            // update the requestInfo in mongo
            reportRequestInfo.setStatus(ReportingRequestStatus.COMPLETED.toString());

            reportingDAL.updateReportRequest(reportRequestInfo.getId(), clientId, reportRequestInfo);
        } catch (Exception e) {
            log.error("Exception occurred : ", e);
            // update the requestInfo in mongo
            reportRequestInfo.setStatus(ReportingRequestStatus.FAILED.toString());
            reportRequestInfo.setErrorMessage(e.getMessage());
            reportingDAL.updateReportRequest(reportRequestInfo.getId(), clientId, reportRequestInfo);
        }

        log.info(LoggingUtils.logMethodExit());
    }

    private void generateAndUploadData(ReportingType reportingType, String fileName, LocalDate startDate, LocalDate endDate, String clientId) throws Exception {
        log.info(LoggingUtils.logMethodEntry(reportingType, fileName, startDate, endDate, clientId));

        switch (reportingType) {
            case AVIATION_BORDEREAU:
                try {
                    generateAppliedBordereauFile(startDate, endDate, clientId, bucketName, fileName);
                } catch (Exception e) {
                    log.error("Error during file upload: ", e);
                    throw e;
                }
                break;

            case AVIATION_SUBMISSION:
                try {
                    // TODO - submission report generate method
                } catch (Exception e) {
                    log.error("Error during file upload: ", e);
                    throw e;
                }
                break;

            default:
                throw new IllegalArgumentException("Unsupported reporting type: " + reportingType);
        }
    }

    private void generateAppliedBordereauFile(LocalDate startDate, LocalDate endDate, String clientId, String bucketName, String fileName) {
        File tempFile = null;
        try {
            //Create a temp file on disk
            tempFile = File.createTempFile(fileName, ".xlsx");

            try (FileOutputStream fileOutputStream = new FileOutputStream(tempFile);
                 SXSSFWorkbook workbook = new SXSSFWorkbook(HUNDRED)) {

                workbook.setCompressTempFiles(true);

                PageRequest pageRequest = PageRequest.of(0, HUNDRED, Sort.by(Sort.Direction.ASC, POLICY_START_DATE));
                Sheet sheet = null;
                List<XlsxField> xlsColumnFields = null;
                int currentRow = 1;  // start from row 1, header is at 0

                while (true) {
                    List<AppliedAviationBordereauReportModel> data = getAppliedAviationBordereauReportData(startDate, endDate, clientId, pageRequest);

                    if (sheet == null) {
                        AppliedAviationBordereauReportModel headerData = new AppliedAviationBordereauReportModel();
                        XlsxSheet annotation = headerData.getClass().getAnnotation(XlsxSheet.class);
                        String sheetName = annotation.value();
                        sheet = workbook.createSheet(sheetName);
                        xlsColumnFields = xlsxWriter.createHeader(headerData, workbook, sheet);
                    }

                    if (CollectionUtils.isEmpty(data)) {
                        break;
                    }


                    currentRow = xlsxWriter.appendRowsToSheet(data, workbook, sheet, currentRow, xlsColumnFields);

                    //next page
                    pageRequest = pageRequest.next();
                }

                workbook.write(fileOutputStream);
                workbook.dispose();
            }
            log.info("Temp file size: {} bytes or {} MB", tempFile.length(), tempFile.length() / (1024 * 1024));

            try (InputStream inputStream = new FileInputStream(tempFile)) {
                storageService.storeObject(bucketName, fileName, inputStream, tempFile.length());
            }
        } catch (Exception e) {
            log.error("Error during report writing or storage upload: ", e);
            throw new RuntimeException("Failed to generate and upload report", e);
        } finally {
            // Clean up the temp file
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (!deleted) {
                    log.error("Could not delete temp file: {}", tempFile.getAbsolutePath());
                }else {
                    log.info("deleted temp file: {}", tempFile.getAbsolutePath());
                }
            }
        }
    }

    /**
     * Retrieves the file data of the requested report based on the specified reporting type,
     * start date, end date, and client ID.
     *
     * @param reportingType The type of report to generate.
     * @param startDate     The start date for the report data.
     * @param endDate       The end date for the report data.
     * @param clientId      The ID of the client for whom the report is generated.
     * @return The byte array containing the file data of the requested report.
     */
    private byte[] getFileDataOfRequestedReport(ReportingType reportingType, LocalDate startDate, LocalDate endDate, String clientId) throws Exception {
        byte[] fileData = new byte[0];

        switch (reportingType) {
            case BORDEREAU -> {
                List<BordereauReportModel> data = getBordereauReportData(startDate, endDate, clientId);

                if (CollectionUtils.isEmpty(data)) {
                    // add blank data for empty file header creation
                    data.add(new BordereauReportModel());
                }
                fileData = xlsxWriter.generateExcel(data);
            }
            case ASU_AGING -> {
                List<ASUAgingReportModel> data = getASUAgingReportData(clientId);

                if (CollectionUtils.isEmpty(data)) {
                    // add blank data for empty file header creation
                    data.add(new ASUAgingReportModel());
                }
                fileData = xlsxWriter.generateExcel(data);
            }
            case PAYABLE_CARRIER -> {
                List<PayableCarrierReportModel> data = getPayableCarrierReportData(startDate, endDate, clientId);

                if (CollectionUtils.isEmpty(data)) {
                    // add blank data for empty file header creation
                    data.add(new PayableCarrierReportModel());
                }
                fileData = xlsxWriter.generateExcel(data);
            }

            case INVOICE_DETAIL -> {
                List<InvoiceDetailReportModel> data = getInvoiceDetailReportData(startDate, endDate, clientId);

                if (CollectionUtils.isEmpty(data)) {
                    // add blank data for empty file header creation
                    data.add(new InvoiceDetailReportModel());
                }
                fileData = xlsxWriter.generateExcel(data);
            }

            case ALL_DATA -> {
                List<AllDataReportModel> data = getAllDataReportData(startDate, endDate, clientId);
                if (CollectionUtils.isEmpty(data)) {
                    // add blank data for empty file header creation
                    data.add(new AllDataReportModel());
                }
                fileData = xlsxWriter.generateExcel(data);
            }

            case ACCOUNT_RECEIVABLE_AGING -> {
                List<AccountReceivableAgingReportModel> data = getAccountReceivableAgingReportData(startDate, endDate, clientId);

                if (CollectionUtils.isEmpty(data)) {
                    // add blank data for empty file header creation
                    data.add(new AccountReceivableAgingReportModel());
                }
                fileData = xlsxWriter.generateExcel(data);
            }
        }

        return fileData;
    }

    /**
     * Creates and initializes a new report request.
     *
     * @param userId   The user ID associated with the report request.
     * @param clientId The client ID associated with the report request.
     * @return The newly created report request information.
     */
    private ReportRequestInfo createReportRequest(String userId, String userName, String clientId) {
        log.info(LoggingUtils.logMethodEntry(clientId));

        ReportRequestInfo reportRequestInfo = new ReportRequestInfo();

        reportRequestInfo.setStatus(ReportingRequestStatus.IN_PROGRESS.toString());
        reportRequestInfo.setClientId(clientId);
        reportRequestInfo.setCreatedBy(userId);
        reportRequestInfo.setCreatedByUserName(userName);
        reportRequestInfo.setCreatedAt(LocalDateTime.now());

        reportRequestInfo = reportingDAL.addReportRequest(reportRequestInfo);

        log.info(LoggingUtils.logMethodExit());
        return reportRequestInfo;
    }

    /**
     * Retrieves Bordereau report data based on the specified date range and client ID.
     *
     * @param startDate The start date for retrieving policies.
     * @param endDate   The end date for retrieving policies.
     * @param clientId  The client ID associated with the policies.
     * @return A list of BordereauReportModel objects representing the Bordereau report data.
     */
    private List<BordereauReportModel> getBordereauReportData(LocalDate startDate, LocalDate endDate, String clientId) {
        log.info(LoggingUtils.logMethodEntry(startDate, endDate, clientId));

        ReportingType reportingType = ReportingType.BORDEREAU;
        PageRequest pageRequest = PageRequest.of(ZERO, HUNDRED, Sort.by(Sort.Direction.ASC, POLICY_START_DATE));

        List<BordereauReportModel> reportDataList = new ArrayList<>();

        while (true) {
            List<Document> policies = policyDAL.getPoliciesBy(startDate, endDate, null, clientId, pageRequest);

            log.info("fetched {} policies info for bordereau report", policies.size());

            if (CollectionUtils.isEmpty(policies)) {
                break;
            }

            // fetch distinct Ids
            Set<String> officeIds = new HashSet<>();
            Set<String> policyIds = new HashSet<>();

            for (Document policy : policies) {
                String officeId = policy.getString(OFFICE_ID);
                String policyId = policy.getString(POLICY_ID);

                // Add officeId if it's not empty
                if (StringUtils.isNotEmpty(officeId)) {
                    officeIds.add(officeId);
                }

                // Add policyId if it's not empty
                if (StringUtils.isNotEmpty(policyId)) {
                    policyIds.add(policyId);
                }
            }

            log.info("fetched {} distinct officeIds for {} polices", officeIds.size(), policies.size());
            log.info("fetched {} distinct policyIds for {} polices", policyIds.size(), policies.size());

            // sal call to brokerage-hub service
            List<Map<String, String>> offices = brokerageHubSAL.getOfficesByIds(officeIds, ZERO, pageRequest.getPageSize(), clientId);

            // create map of officeId and officeResponse
            Map<String, Map<String, String>> officesMap = offices.stream()
                    .collect(Collectors.toMap(
                            office -> office.get(OFFICE_ID),
                            Function.identity()
                    ));


            // create map of policyId and policy
            Map<String, Document> policiesMap = policies.stream()
                    .collect(Collectors.toMap(
                            policy -> policy.get(POLICY_ID).toString(),
                            Function.identity()
                    ));


            Map<String, List<Document>> endorsementsMap = endorsementDAL.getSortedEndorsementsGroupedByPolicy(policyIds, BOUND_ENDORSEMENT_STATUSES, clientId, false, reportingType);

            endorsementsMap = filterEndorsementsUpToLastCancelRewriteInPolicyBoundMonth(endorsementsMap);

            log.info("Fetched endorsements for bordereau report across {} policies.", endorsementsMap.size());

            Map<String, Document> policyIdToBoundPolicyDocumentMap = new HashMap<>();

            PageRequest auditPageRequest = PageRequest.of(ZERO, HUNDRED, Sort.by(Sort.Direction.DESC, PAYLOAD_UPDATED_AT));

            for(String policyId : policyIds) {
                Document boundPolicyDocument = auditDAL.getPoliciesBy(policyId, BOUND, clientId, auditPageRequest);
                if (boundPolicyDocument == null) {
                    log.error("No document found for policyId: {}", policyId);
                    continue;
                }

                // Access the 'payload' document
                Document payload = boundPolicyDocument.get("payload", Document.class);
                if (payload == null) {
                    log.error("Payload is null for document: {}", boundPolicyDocument);
                    continue;
                }

                // Access the 'policyId' within the 'payload'
                String policyKey = payload.getString("policyId");
                if (policyKey == null) {
                    log.error("PolicyId is null in payload for document: {}", boundPolicyDocument);
                    continue;
                }

                // Populate the map with valid keys
                policyIdToBoundPolicyDocumentMap.put(policyKey, boundPolicyDocument);
            }

            reportDataList.addAll(buildBordereauReportList(policies, officesMap, endorsementsMap, policyIdToBoundPolicyDocumentMap));

            // next page
            pageRequest = pageRequest.next();
        }

        log.info(LoggingUtils.logMethodExit());

        // return
        return reportDataList;
    }

    private List<AllDataReportModel> getAllDataReportData(LocalDate startDate, LocalDate endDate, String clientId) {
        log.info(LoggingUtils.logMethodEntry(startDate, endDate, clientId));

        ReportingType reportingType = ReportingType.ALL_DATA;
        PageRequest pageRequest = PageRequest.of(ZERO, HUNDRED, Sort.by(Sort.Direction.ASC, POLICY_START_DATE));

        List<AllDataReportModel> reportDataList = new ArrayList<>();

        while (true) {
            //Policy data
            List<Document> policies = policyDAL.getPoliciesBy(startDate, endDate, null, clientId, pageRequest);

            log.info("fetched {} policies info for All data report", policies.size());

            if (CollectionUtils.isEmpty(policies)) {
                break;
            }

            // fetch distinct policyIds
            Set<String> policyIds = new HashSet<>();

            for (Document policy : policies) {
                String policyId = policy.getString(POLICY_ID);

                // Add policyId if it's not empty
                if (StringUtils.isNotEmpty(policyId)) {
                    policyIds.add(policyId);
                }
            }

            log.info("fetched {} distinct policyIds for {} polices", policyIds.size(), policies.size());

            Map<String, List<Document>> endorsementsMap = endorsementDAL.getSortedEndorsementsGroupedByPolicy(policyIds, BOUND_ENDORSEMENT_STATUSES, clientId, true, reportingType);

            log.info("fetched {} endorsements for All data report", endorsementsMap.size());

            // create map of policyId and list of endorsements
//            Map<String, List<Document>> endorsementsMap = endorsements.stream()
//                    .collect(Collectors.groupingBy(
//                            ed -> ed.getString(POLICY_ID) // Group by policyId
//                    ));

            reportDataList.addAll(buildAllDataReportList(policies, endorsementsMap));

            // next page
            pageRequest = pageRequest.next();
        }

        log.info(LoggingUtils.logMethodExit());

        // return
        return reportDataList;
    }

    /**
     * Retrieves ASU Aging report data for the specified client ID.
     *
     * @param clientId The ID of the client for whom ASU Aging report data is retrieved.
     * @return A list of ASUAgingReportModel objects representing the ASU Aging report data.
     */
    private List<ASUAgingReportModel> getASUAgingReportData(String clientId) {
        log.info(LoggingUtils.logMethodEntry(clientId));

        PageRequest pageRequest = PageRequest.of(ZERO, HUNDRED, Sort.by(Sort.Direction.ASC, POLICY_START_DATE));
        List<ASUAgingReportModel> reportDataList = new ArrayList<>();

        while (true) {
            List<Document> policies = policyDAL.getPoliciesBy(null, null, false, clientId, pageRequest);

            log.info("fetched {} policies info for asu report", policies.size());

            if (CollectionUtils.isEmpty(policies)) {
                break;
            }

            // fetch policyIds
            List<String> policyIds = policies.stream()
                    .filter(document -> StringUtils.isNotEmpty(document.getString(POLICY_ID)))
                    .map(document -> document.getString(POLICY_ID)).toList();

            // fetch endorsements
            List<Document> endorsements = endorsementDAL.getInvoicedPolicyEndorsementsByPolicyIdsAndClient(policyIds, clientId);

            log.info("fetched {} endorsements for asu aging report", endorsements.size());

            // fetch invoiceIds
            List<String> invoiceIds = endorsements.stream()
                    .filter(document -> StringUtils.isNotEmpty(document.getString(INVOICE_ID)))
                    .map(document -> document.getString(INVOICE_ID)).toList();

            // sal call to invoice service
            List<Map<String, String>> invoices = invoiceSAL.searchInvoicesBy(invoiceIds, ZERO, endorsements.size(), clientId);

            // create map of invoiceId and invoiceResponse
            Map<String, Map<String, String>> invoicesMap = invoices.stream()
                    .collect(Collectors.toMap(map -> map.get(RESPONSE_ID), Function.identity()));

            reportDataList.addAll(buildASUAgingReportList(endorsements, invoicesMap));

            // next page
            pageRequest = pageRequest.next();
        }

        log.info(LoggingUtils.logMethodExit());

        // return
        return reportDataList;
    }

    /**
     * Retrieves the payable carrier report data for the specified date range and client ID.
     *
     * @param startDate The start date of the report data.
     * @param endDate   The end date of the report data.
     * @param clientId  The ID of the client for whom the report data is fetched.
     * @return A list of {@code PayableCarrierReportModel} objects representing the payable carrier report data.
     */
    private List<PayableCarrierReportModel> getPayableCarrierReportData(LocalDate startDate, LocalDate endDate, String clientId) {
        log.info(LoggingUtils.logMethodEntry(startDate, endDate, clientId));

        PageRequest pageRequest = PageRequest.of(ZERO, HUNDRED, Sort.by(Sort.Direction.ASC, ENDORSEMENT_EFFECTIVE_DATE));
        List<PayableCarrierReportModel> reportDataList = new ArrayList<>();

        while (true) {
            List<Document> endorsements = endorsementDAL.getPolicyEndorsementsBy(startDate, endDate, BOUND_ENDORSEMENT_STATUSES, true, true, clientId, pageRequest);

            log.info("fetched {} endorsements for payable to the carrier report", endorsements.size());

            if (CollectionUtils.isEmpty(endorsements)) {
                break;
            }

            // fetch invoiceIds
            List<String> invoiceIds = endorsements.stream()
                    .filter(document -> StringUtils.isNotEmpty(document.getString(INVOICE_ID)))
                    .map(document -> document.getString(INVOICE_ID)).toList();

            // sal call to invoice service
            List<Map<String, String>> invoices = invoiceSAL.searchInvoicesBy(invoiceIds, ZERO, pageRequest.getPageSize(), clientId);

            // create map of invoiceId and invoiceResponse
            Map<String, Map<String, String>> invoicesMap = invoices.stream()
                    .collect(Collectors.toMap(map -> map.get(RESPONSE_ID), Function.identity()));

            reportDataList.addAll(buildPayableCarrierReportList(endorsements, invoicesMap));

            // next page
            pageRequest = pageRequest.next();
        }

        log.info(LoggingUtils.logMethodExit());
        return reportDataList;
    }

    /**
     * Retrieves the invoice detail report data within the specified date range for the given client ID.
     *
     * @param startDate The start date for the invoice detail report.
     * @param endDate   The end date for the invoice detail report.
     * @param clientId  The ID of the client for whom the report is generated.
     * @return A list of InvoiceDetailReportModel objects representing the invoice detail report data.
     */
    private List<InvoiceDetailReportModel> getInvoiceDetailReportData(LocalDate startDate, LocalDate endDate, String clientId) {
        log.info(LoggingUtils.logMethodEntry(startDate, endDate, clientId));

        PageRequest pageRequest = PageRequest.of(ZERO, HUNDRED, Sort.by(Sort.Direction.ASC, POLICY_START_DATE));

        List<InvoiceDetailReportModel> reportDataList = new ArrayList<>();

        while (true) {
            List<Document> policies = policyDAL.getPoliciesBy(startDate, endDate, null, clientId, pageRequest);
            log.info("fetched {} policies for invoice detail report", policies.size());

            if (CollectionUtils.isEmpty(policies)) {
                break;
            }

            List<String> policyIds = policies.stream().map(document -> document.getString(POLICY_ID)).toList();

            // Service call to the audit-service to retrieve the latest BIND policy information of each policy using the policyIds
            List<PolicySubmissionAuditDTO> auditPolicies = auditSAL.getRecentAuditPolicesByPolicyIds(policyIds, "BIND", ZERO, pageRequest.getPageSize(), Sort.Direction.DESC.name(), CREATED_AT, clientId);

            List<Document> recentPolicies = auditPolicies.stream().map(PolicySubmissionAuditDTO::getData)
                    .map(Document::new)
                    .collect(Collectors.toList());

            List<String> policyInvoiceIds = recentPolicies.stream()
                    .filter(document -> StringUtils.isNotEmpty(document.getString(INVOICE_ID)))
                    .map(document -> document.getString(INVOICE_ID)).toList();


            // sal call to invoice service
            List<Map<String, String>> invoices = invoiceSAL.searchInvoicesBy(policyInvoiceIds, ZERO, pageRequest.getPageSize(), clientId);

            // create map of invoiceId and invoiceResponse
            Map<String, Map<String, String>> invoicesMap = invoices.stream()
                    .collect(Collectors.toMap(map -> map.get(RESPONSE_ID), Function.identity()));

            // build invoice report data from policy doc
            reportDataList.addAll(buildInvoiceDetailReportListFromPolicy(recentPolicies, invoicesMap));

            // next page
            pageRequest = pageRequest.next();
        }

        // reset the page again to 0 for endorsement fetching
        pageRequest = PageRequest.of(ZERO, HUNDRED, Sort.by(Sort.Direction.ASC, ENDORSEMENT_EFFECTIVE_DATE));

        while (true) {
            List<Document> endorsements = endorsementDAL.getPolicyEndorsementsBy(startDate, endDate, BOUND_ENDORSEMENT_STATUSES, true, true, clientId, pageRequest);

            log.info("fetched {} endorsements for invoice detail report", endorsements.size());

            if (CollectionUtils.isEmpty(endorsements)) {
                break;
            }

            // fetch invoiceIds
            List<String> invoiceIds = endorsements.stream()
                    .filter(document -> StringUtils.isNotEmpty(document.getString(INVOICE_ID)))
                    .map(document -> document.getString(INVOICE_ID)).toList();

            // sal call to invoice service
            List<Map<String, String>> invoices = invoiceSAL.searchInvoicesBy(invoiceIds, ZERO, pageRequest.getPageSize(), clientId);

            // create map of invoiceId and invoiceResponse
            Map<String, Map<String, String>> invoicesMap = invoices.stream()
                    .collect(Collectors.toMap(map -> map.get(RESPONSE_ID), Function.identity()));

            reportDataList.addAll(buildInvoiceDetailReportListFromPolicyEndorsement(endorsements, invoicesMap));

            // next page
            pageRequest = pageRequest.next();
        }

        log.info(LoggingUtils.logMethodExit());
        return reportDataList;
    }

    /**
     * Retrieves information about a specific report request, including a pre-signed URL for downloading the report file.
     *
     * @param requestId The ID of the report request to retrieve.
     * @param clientId  The client ID associated with the report request.
     * @return A ReportRequestInfoDTO containing information about the specified report request.
     */
    @Override
    public ReportRequestInfoDTO getReportRequestInfoById(String requestId, String clientId) {
        log.info(LoggingUtils.logMethodEntry(requestId, clientId));

        ReportRequestInfo reportRequestInfo = reportingDAL.getReportRequestById(requestId, clientId);

        if (reportRequestInfo.getStatus().equals(ReportingRequestStatus.IN_PROGRESS.toString())) {
            log.error("Report Status - {}", reportRequestInfo.getStatus());
            throw new TooEarlyRequestException(String.format("Report Status - %s", reportRequestInfo.getStatus()));
        }

        if (reportRequestInfo.getStatus().equals(ReportingRequestStatus.COMPLETED.toString())) {
            // generate the PreSignedUrl
            String generatePreSignedUrl = storageService.generatePreSignedUrl(bucketName, reportRequestInfo.getFileName(), reportLinkExpirationInDays);

            reportRequestInfo.setFileDownloadLink(generatePreSignedUrl);
        }

        log.info(LoggingUtils.logMethodExit());
        return ReportRequestInfoDTO.toDTO(reportRequestInfo);
    }

    private List<AccountReceivableAgingReportModel> getAccountReceivableAgingReportData(LocalDate startDate, LocalDate endDate, String clientId) {
        log.info(LoggingUtils.logMethodEntry(startDate, endDate, clientId));

        PageRequest pageRequest = PageRequest.of(ZERO, HUNDRED, Sort.by(Sort.Direction.ASC, POLICY_START_DATE));
        List<AccountReceivableAgingReportModel> reportDataList = new ArrayList<>();

        while (true) {
            List<Document> policies = policyDAL.getPoliciesByInvoiceDate(startDate, endDate, clientId, pageRequest);

            log.info("fetched {} policies info for account receivable aging report", policies.size());

            if (CollectionUtils.isEmpty(policies)) {
                break;
            }

            reportDataList.addAll(buildAccountReceivableAgingReportList(policies));

            // next page
            pageRequest = pageRequest.next();
        }

        log.info(LoggingUtils.logMethodExit());

        // return
        return reportDataList;
    }

    private List<AppliedAviationBordereauReportModel> getAppliedAviationBordereauReportData(LocalDate startDate, LocalDate endDate, String clientId, PageRequest pageRequest) {
        log.info(LoggingUtils.logMethodEntry(startDate, endDate, clientId, pageRequest));

        List<AppliedAviationBordereauReportModel> reportDataList = new ArrayList<>();

        List<Document> policies = policyDAL.getPoliciesByEffectiveDate(startDate, endDate, clientId, pageRequest);

        log.info("fetched {} policies info for applied aviation bordereau report", policies.size());

        if (CollectionUtils.isEmpty(policies)) {
            return reportDataList;
        }

        reportDataList = AppliedUtilityMapper.buildAppliedAviationBordereauReportModelList(policies);

        log.info(LoggingUtils.logMethodExit());

        // return
        return reportDataList;
    }

    /**
     * Retrieves a paginated list of report request information for a specific client.
     *
     * @param pageRequest The pagination information.
     * @param clientId    The client ID associated with the report requests.
     * @return A list of ReportRequestInfoDTO objects representing the paginated report request information.
     */
    @Override
    public Pair<Long, List<ReportRequestInfoDTO>> getPairOfTotalCountAndReportRequestInfoList(PageRequest pageRequest, String clientId) {
        log.info(LoggingUtils.logMethodEntry(pageRequest, clientId));

        // last 7 days reports
        LocalDate date = LocalDate.now().minusDays(lastDaysOfReport);

        Pair<Long, List<ReportRequestInfo>> totalCountAndPaginatedReportRequest = reportingDAL.getPairOfTotalCountAndReportRequestInfoList(pageRequest, clientId, date);

        Long totalCount = totalCountAndPaginatedReportRequest.getLeft();
        List<ReportRequestInfo> reportRequestInfoList = totalCountAndPaginatedReportRequest.getRight();
        List<ReportRequestInfoDTO> dtos = reportRequestInfoList.stream().map(ReportRequestInfoDTO::toDTO).toList();

        log.info(LoggingUtils.logMethodExit());
        return Pair.of(totalCount, dtos);
    }

    /**
     * Deletes reports from both Amazon S3 and MongoDB that are older than 30 days.
     * Reports are fetched in batches using pagination and deleted iteratively until no more reports are found.
     * Each batch consists of expired reports fetched from the MongoDB collection based on the specified criteria.
     * For each batch, the associated files are deleted from the specified Amazon S3 bucket, and the corresponding
     * report request information is deleted from the MongoDB collection.
     * Any exceptions that occur during the deletion process are logged.
     */
    @Override
    public void deleteReports() {
        // fetch reports older than last 30 days
        LocalDate date = LocalDate.now().minusDays(30);

        PageRequest pageRequest = PageRequest.of(ZERO, HUNDRED);

        try {
            while (true) {

                List<ReportRequestInfo> expiredReports = reportingDAL.getReportRequestInfoListBeforeDate(pageRequest, null, date);

                log.info("fetched {} expired reports that were before date '{}'", expiredReports.size(), date);

                if (CollectionUtils.isEmpty(expiredReports)) {
                    break;
                }

                // fetch objectKey = fileName
                List<String> objectKeys = expiredReports.stream().map(ReportRequestInfo::getFileName).toList();

                // delete files from storage
                storageService.deleteMultipleObjects(bucketName, objectKeys);

                // fetch mongo id (= _id)
                List<String> mongoIds = expiredReports.stream().map(ReportRequestInfo::getId).toList();

                // delete report requests from mongo
                reportingDAL.deleteReportRequestInfoByIds(mongoIds);

                // next page
                pageRequest = pageRequest.next();
            }
        } catch (Exception e) {
            log.error("Exception occurred in delete reports from S3 and mongo - ", e);
        }
        log.info(LoggingUtils.logMethodExit());
    }


    /**
     * Creates a submission extract report request.
     * publish an event to start async submission extract report generation process
     *
     * @param submissionRequestDTO The submission data transfer object containing submission details.
`     * @param clientId            The client ID associated with the report request.
     * @return The ID of the created submission extract report request.
     */
    @Override
    public String createSubmissionExtractReport(SubmissionRequestDTO submissionRequestDTO, String clientId) {
        log.info(LoggingUtils.logMethodEntry(submissionRequestDTO.getSubmissionId(), clientId));

        // create report request
        ReportRequestInfo reportRequestInfo = createReportRequest(submissionRequestDTO.getSubmissionId(), submissionRequestDTO.getModel(), clientId);

        //set file name
        String fileName = generateSubmissionExtractFileName(submissionRequestDTO, reportRequestInfo.getId());
        reportRequestInfo.setFileName(fileName);

        //set report type
        reportRequestInfo.setReportType(ReportingType.WHITESPACE_SUBMISSION.name());

        //set run date (no start/end dates for submission extract)
        reportRequestInfo.setRunDate(LocalDate.now());

        reportRequestInfo = reportingDAL.updateReportRequest(reportRequestInfo.getId(), clientId, reportRequestInfo);

        // publish event (it will process asynchronously)
        eventPublisher.publishEvent(new SubmissionExtractionEvent(this, submissionRequestDTO, clientId, reportRequestInfo));

        log.info(LoggingUtils.logMethodExit());
        return reportRequestInfo.getId();
    }

    /**
     * Generates submission extract report with multiple sheets from JSON payload.
     *
     * @param submissionRequestDTO The submission data containing all information.
     * @param clientId            The client ID for whom the report is generated.
     * @param reportRequestInfo   Information about the report request.
     */
    @Override
    public void generateSubmissionExtractReport(SubmissionRequestDTO submissionRequestDTO, String clientId, ReportRequestInfo reportRequestInfo) {
        log.info(LoggingUtils.logMethodEntry(submissionRequestDTO.getSubmissionId(), clientId));

        try {
            // Transform JSON data to Excel models
            List<RiskDetailsWhitespaceReportModel> riskDetails = transformRiskDetails(submissionRequestDTO);
            List<PilotInfoReportModel> pilotInfo = transformPilotInfo(submissionRequestDTO);
            List<AircraftInfoReportModel> aircraftInfo = transformAircraftInfo(submissionRequestDTO);
            List<AdditionalInsuredInfoReportModel> additionalInsuredInfo = transformAdditionalInsuredInfo(submissionRequestDTO);

            // Generate multi-sheet Excel using streaming approach
            String reportBase64Content = generateSubmissionMultiSheetExcel(reportRequestInfo.getFileName(), riskDetails, pilotInfo, aircraftInfo, additionalInsuredInfo, clientId);
            
            // Send email notification with report attachment
            reportNotificationService.sendReportGenerationEmail(reportRequestInfo.getFileName(),clientId, reportBase64Content, submissionRequestDTO);

            // update the requestInfo in mongo
            reportRequestInfo.setStatus(ReportingRequestStatus.COMPLETED.toString());
            reportingDAL.updateReportRequest(reportRequestInfo.getId(), clientId, reportRequestInfo);

        } catch (Exception e) {
            log.error("Exception occurred during submission extract report generation: ", e);
            // update the requestInfo in mongo
            reportRequestInfo.setStatus(ReportingRequestStatus.FAILED.toString());
            reportRequestInfo.setErrorMessage(e.getMessage());
            reportingDAL.updateReportRequest(reportRequestInfo.getId(), clientId, reportRequestInfo);
        }

        log.info(LoggingUtils.logMethodExit());
    }

    private String generateSubmissionMultiSheetExcel(String fileName,
                                                    List<RiskDetailsWhitespaceReportModel> riskDetails,
                                                    List<PilotInfoReportModel> pilotInfo,
                                                    List<AircraftInfoReportModel> aircraftInfo,
                                                    List<AdditionalInsuredInfoReportModel> additionalInsuredInfo,
                                                    String clientId) throws Exception {

        File tempFile = null;
        try {
            tempFile = File.createTempFile(fileName, ".xlsx");

            try (FileOutputStream fileOutputStream = new FileOutputStream(tempFile);
                 SXSSFWorkbook workbook = new SXSSFWorkbook(HUNDRED)) {

                workbook.setCompressTempFiles(true);

                // Create Risk Details sheet (multiple rows for 3-column format)
                if (!riskDetails.isEmpty()) {
                    createMultiRowSheet(workbook, "Risk Details", riskDetails);
                }

                // Create Pilot Information sheet
                if (!pilotInfo.isEmpty()) {
                    createMultiRowSheet(workbook, "Pilot Information", pilotInfo);
                }

                // Create Aircraft Information sheet
                if (!aircraftInfo.isEmpty()) {
                    createMultiRowSheet(workbook, "Aircraft Information", aircraftInfo);
                }

                // Create Additional Insured Information sheet
                if (!additionalInsuredInfo.isEmpty()) {
                    createMultiRowSheet(workbook, "Additional Insured Information", additionalInsuredInfo);
                }

                workbook.write(fileOutputStream);
                workbook.dispose();
            }

            // Upload to storage
            try (InputStream inputStream = new FileInputStream(tempFile)) {
                storageService.storeObject(bucketName, fileName, inputStream, tempFile.length());
                log.info("Successfully uploaded submission extract Excel file: {}", fileName);
            }

            // Read the file and encode to base64 for email attachment
            byte[] fileContent;
            try (FileInputStream fileInputStream = new FileInputStream(tempFile)) {
                fileContent = fileInputStream.readAllBytes();
            }
            
            return Base64.getEncoder().encodeToString(fileContent);

        } finally {
            // Clean up temp file
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (!deleted) {
                    log.error("Could not delete temp file: {}", tempFile.getAbsolutePath());
                } else {
                    log.info("Deleted temp file: {}", tempFile.getAbsolutePath());
                }
            }
        }
    }

    private <T> void createMultiRowSheet(SXSSFWorkbook workbook, String sheetName, List<T> data) throws Exception {
        if (data.isEmpty()) return;

        Sheet sheet = workbook.createSheet(sheetName);
        List<XlsxField> xlsColumnFields = xlsxWriter.createHeader(data.get(0), workbook, sheet);
        xlsxWriter.appendRowsToSheet(data, workbook, sheet, 1, xlsColumnFields);
    }

    private <T> void createSingleRowSheet(SXSSFWorkbook workbook, String sheetName, T data) throws Exception {
        Sheet sheet = workbook.createSheet(sheetName);
        List<XlsxField> xlsColumnFields = xlsxWriter.createHeader(data, workbook, sheet);
        xlsxWriter.appendRowsToSheet(List.of(data), workbook, sheet, 1, xlsColumnFields);
    }

    // Transformation methods
    private List<RiskDetailsWhitespaceReportModel> transformRiskDetails(SubmissionRequestDTO submissionRequestDTO) {
        try {
            if (submissionRequestDTO.getSubmissionBody() == null ||
                submissionRequestDTO.getSubmissionBody().getBody() == null ||
                submissionRequestDTO.getSubmissionBody().getBody().getRiskDetails() == null) {
                return new ArrayList<>();
            }

            var riskDetails = submissionRequestDTO.getSubmissionBody().getBody().getRiskDetails();
            var confidenceLevel = submissionRequestDTO.getSubmissionBody().getBody().getConfidenceLevel();
            Map<String, Object> riskDetailsConfidence = confidenceLevel != null ? (Map<String, Object>) confidenceLevel.get("riskDetails") : null;

            return buildRiskDetailsReport(riskDetails, riskDetailsConfidence);
        } catch (Exception e) {
            log.warn("Error transforming risk details: ", e);
            return new ArrayList<>();
        }
    }

    private List<RiskDetailsWhitespaceReportModel> buildRiskDetailsReport(Object riskDetails, Map<String, Object> confidenceMap) {
        List<RiskDetailsWhitespaceReportModel> result = new ArrayList<>();
        
        if (riskDetails == null) {
            return result;
        }

        log.info("Processing riskDetails of type: {}", riskDetails.getClass().getName());

        // Field name mappings
        Map<String, String> fieldDisplayNames = Map.ofEntries(
            Map.entry("umr", "UMR"),
            Map.entry("type", "Risk Type"),
            Map.entry("insured", "Insured Name"),
            Map.entry("insuredLineItemDescription", "Insured Line Item Description"),
            Map.entry("insuredNumberAndStreet", "Insured Address"),
            Map.entry("insuredAdditionalAddressLine", "Insured Additional Address"),
            Map.entry("insuredCityName", "Insured City"),
            Map.entry("insuredPostalCode", "Insured Postal Code"),
            Map.entry("insuredCountry", "Insured Country"),
            Map.entry("inceptionDate", "Inception Date"),
            Map.entry("expiryDate", "Expiry Date"),
            Map.entry("interest", "Interest"),
            Map.entry("limitOfLiability", "Limit of Liability"),
            Map.entry("reinsuredsRetention", "Reinsured's Retention"),
            Map.entry("coverageLimit", "Coverage Limit"),
            Map.entry("excess", "Excess"),
            Map.entry("sumInsuredLineItemDescription", "Sum Insured Line Item Description"),
            Map.entry("situation", "Situation"),
            Map.entry("situationLineItemDescription", "Situation Line Item Description"),
            Map.entry("choiceOfLaw", "Choice of Law"),
            Map.entry("jurisdictionLocationCountry", "Jurisdiction Location Country"),
            Map.entry("premium", "Premium"),
            Map.entry("premiumLineItemDescription", "Premium Line Item Description"),
            Map.entry("instalmentAmount", "Instalment Amount"),
            Map.entry("instalmentPercentage", "Instalment Percentage"),
            Map.entry("instalmentDueDate", "Instalment Due Date"),
            Map.entry("orderHereon", "Order Hereon"),
            Map.entry("orderHereonLineItemDescription", "Order Hereon Line Item Description"),
            Map.entry("basisOfWrittenLines", "Basis of Written Lines"),
            Map.entry("slipLeader", "Slip Leader"),
            Map.entry("bureauLeader", "Bureau Leader"),
            Map.entry("bureauLeaderLineItemDescription", "Bureau Leader Line Item Description"),
            Map.entry("basisOfClaimsAgreement", "Basis of Claims Agreement"),
            Map.entry("settlementDueDate", "Settlement Due Date"),
            Map.entry("taxPayableByInsurers", "Tax Payable by Insurers"),
            Map.entry("countryOfOrigin", "Country of Origin"),
            Map.entry("correspondentBrokerName", "Correspondent Broker Name"),
            Map.entry("otherBrokerAdditionalAddressLine", "Other Broker Additional Address"),
            Map.entry("otherBrokerAddressCityName", "Other Broker Address City"),
            Map.entry("otherBrokerAddressPostalCode", "Other Broker Address Postal Code"),
            Map.entry("otherBrokerAddressCountry", "Other Broker Address Country"),
            Map.entry("riskCode", "Risk Code"),
            Map.entry("riskCodePercentage", "Risk Code Percentage"),
            Map.entry("riskCodeLineItemDescription", "Risk Code Line Item Description"),
            Map.entry("regulatoryClientClassification", "Regulatory Client Classification"),
            Map.entry("feePayableByClient", "Fee Payable by Client"),
            Map.entry("totalBrokerage", "Total Brokerage"),
            Map.entry("otherDeductionsFromPremium", "Other Deductions from Premium"),
            Map.entry("otherDeductionsFromPremiumLineItemDescription", "Other Deductions from Premium Line Item Description")
        );

        // Handle both Map and POJO cases
        if (riskDetails instanceof Map) {
            log.info("Processing riskDetails as Map with {} entries", ((Map<?, ?>) riskDetails).size());
            Map<String, Object> riskDetailsMap = (Map<String, Object>) riskDetails;
            
            for (Map.Entry<String, Object> entry : riskDetailsMap.entrySet()) {
                String fieldName = entry.getKey();
                Object value = entry.getValue();
                
                String displayName = fieldDisplayNames.getOrDefault(fieldName, 
                    convertCamelCaseToTitle(fieldName));
                
                Object confidenceScore = confidenceMap != null ? confidenceMap.get(fieldName) : null;
                String displayValue = formatFieldValue(value);
                
                result.add(new RiskDetailsWhitespaceReportModel(
                    displayName,       // fieldName
                    displayValue,      // mappedValue (formatted for arrays)
                    formatConfidenceScore(confidenceScore)  // confidenceScore
                ));
            }
        } else {
            log.info("Processing riskDetails as POJO using reflection");
            // Use reflection to get all fields dynamically
            Class<?> clazz = riskDetails.getClass();
            java.lang.reflect.Field[] fields = clazz.getDeclaredFields();
            
            log.info("Found {} fields in riskDetails class", fields.length);
            
            for (java.lang.reflect.Field field : fields) {
                try {
                    field.setAccessible(true);
                    Object value = field.get(riskDetails);
                    
                    String fieldName = field.getName();
                    String displayName = fieldDisplayNames.getOrDefault(fieldName, 
                        convertCamelCaseToTitle(fieldName));
                    
                    Object confidenceScore = confidenceMap != null ? confidenceMap.get(fieldName) : null;
                    String displayValue = formatFieldValue(value);
                    
                    result.add(new RiskDetailsWhitespaceReportModel(
                        displayName,       // fieldName
                        displayValue,      // mappedValue (formatted for arrays)
                        formatConfidenceScore(confidenceScore)  // confidenceScore
                    ));
                    
                    log.debug("Added field: {} = {}", fieldName, displayValue);
                } catch (IllegalAccessException e) {
                    log.warn("Unable to access field {}: {}", field.getName(), e.getMessage());
                }
            }
        }
        
        log.info("Built {} risk details entries for report", result.size());
        return result;
    }

    private String convertCamelCaseToTitle(String camelCase) {
        if (camelCase == null || camelCase.isEmpty()) {
            return camelCase;
        }
        
        StringBuilder result = new StringBuilder();
        result.append(Character.toUpperCase(camelCase.charAt(0)));
        
        for (int i = 1; i < camelCase.length(); i++) {
            char c = camelCase.charAt(i);
            if (Character.isUpperCase(c)) {
                result.append(' ');
            }
            result.append(c);
        }
        
        return result.toString();
    }

    private String formatFieldValue(Object value) {
        if (value == null) {
            return "";
        }
        
        if (value instanceof List) {
            List<?> list = (List<?>) value;
            if (list.isEmpty()) {
                return "";
            }
            
            // Join array elements with comma separation
            return list.stream()
                .map(item -> item != null ? item.toString() : "")
                .collect(Collectors.joining(", "));
        } else if (value.toString().trim().isEmpty()) {
            return "";
        } else {
            return value.toString();
        }
    }

    private String formatConfidenceScore(Object confidenceValue) {
        if (confidenceValue == null) {
            return "N/A";
        }
        
        try {
            if (confidenceValue instanceof List) {
                List<?> list = (List<?>) confidenceValue;
                if (list.isEmpty()) {
                    return "N/A";
                }
                
                // Format each confidence value and join with comma
                return list.stream()
                    .map(this::formatSingleConfidenceScore)
                    .collect(Collectors.joining(", "));
            } else {
                return formatSingleConfidenceScore(confidenceValue);
            }
        } catch (Exception e) {
            log.warn("Error formatting confidence score: {}", confidenceValue, e);
            return "N/A";
        }
    }
    
    private String formatSingleConfidenceScore(Object confidenceValue) {
        if (confidenceValue == null) {
            return "N/A";
        }
        
        try {
            if (confidenceValue instanceof String) {
                String strValue = (String) confidenceValue;
                if ("1".equals(strValue) || "1.0".equals(strValue)) {
                    return "100%";
                }
                double value = Double.parseDouble(strValue);
                if (value <= 1.0) {
                    return String.format("%.2f%%", value * 100);
                } else {
                    return String.format("%.2f%%", value);
                }
            } else if (confidenceValue instanceof Number) {
                double value = ((Number) confidenceValue).doubleValue();
                if (value <= 1.0) {
                    return String.format("%.2f%%", value * 100);
                } else {
                    return String.format("%.2f%%", value);
                }
            } else {
                return confidenceValue.toString();
            }
        } catch (Exception e) {
            log.warn("Error formatting single confidence score: {}", confidenceValue, e);
            return "N/A";
        }
    }

    private List<PilotInfoReportModel> transformPilotInfo(SubmissionRequestDTO submissionRequestDTO) {
        try {
            if (submissionRequestDTO.getSubmissionBody() == null ||
                submissionRequestDTO.getSubmissionBody().getBody() == null ||
                submissionRequestDTO.getSubmissionBody().getBody().getPilotInfo() == null) {
                return new ArrayList<>();
            }

            var pilotInfoList = submissionRequestDTO.getSubmissionBody().getBody().getPilotInfo();

            return pilotInfoList.stream()
                .map(pilot -> new PilotInfoReportModel(
                    pilot.getName(),
                    pilot.getDob(),
                    pilot.getAge(),
                    pilot.getDateOfCompletion(),
                    pilot.getTrainingFacility(),
                    pilot.getTotalHours(),
                    pilot.getPilotFlightHours_me(),
                    pilot.getPilotFlightHours_se(),
                    pilot.getPilotFlightHours_mm(),
                    pilot.getPilotFlightHours_fw(),
                    pilot.getPilotFlightHours_rw(),
                    pilot.getPilotFlightHours_rg(),
                    pilot.getPilotFlightHours_tp(),
                    pilot.getPilotFlightHours_tj(),
                    formatConfidenceScore(pilot.getConfidence())
                ))
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("Error transforming pilot info: ", e);
            return new ArrayList<>();
        }
    }

    private List<AircraftInfoReportModel> transformAircraftInfo(SubmissionRequestDTO submissionRequestDTO) {
        try {
            if (submissionRequestDTO.getSubmissionBody() == null ||
                submissionRequestDTO.getSubmissionBody().getBody() == null ||
                submissionRequestDTO.getSubmissionBody().getBody().getAircraftInformation() == null) {
                return new ArrayList<>();
            }

            var aircraftInfoList = submissionRequestDTO.getSubmissionBody().getBody().getAircraftInformation();

            return aircraftInfoList.stream()
                .map(aircraft -> new AircraftInfoReportModel(
                    aircraft.getRegistrationNumber(),
                    aircraft.getRegistrationAgencyName(),
                    aircraft.getRegistrationLocationCountry(),
                    aircraft.getSerialNumber(),
                    aircraft.getCertificationNumber(),
                    aircraft.getInsurableInterestType(),
                    aircraft.getInsurableInterestTypeAdditionalTaxGrain(),
                    aircraft.getInsurableInterestUsage(),
                    aircraft.getInsurableInterestTransitStorageCondition(),
                    aircraft.getAircraftType(),
                    aircraft.getWeightKgs(),
                    aircraft.getBuildYear(),
                    aircraft.getEngines(),
                    aircraft.getWingType(),
                    aircraft.getSelVsMel(),
                    aircraft.getNumberOfSeatsCrew(),
                    aircraft.getNumberOfSeatsPassengers(),
                    aircraft.getCallSign(),
                    aircraft.getLessor(),
                    aircraft.getManufacturer(),
                    aircraft.getAreasOfOperation(),
                    aircraft.getBase(),
                    aircraft.getPurchasePrice(),
                    aircraft.getDateOfPurchase(),
                    aircraft.getSumInsured(),
                    aircraft.getInsuredFrom(),
                    aircraft.getInsuredTo(),
                    aircraft.getExcess(),
                    aircraft.getPremium(),
                    aircraft.getHullValue(),
                    aircraft.getNimDed(),
                    aircraft.getImDed(),
                    aircraft.getReqLiabLimits(),
                    aircraft.getReqSubLimits(),
                    aircraft.getMedicalExpLimit(),
                    aircraft.getUtilAnnFlightHrs(),
                    aircraft.getHullWar(),
                    aircraft.getTriaHull(),
                    aircraft.getWarLiab(),
                    aircraft.getTriaLiab(),
                    aircraft.getExcessWar(),
                    aircraft.getTerritoryAreaOfOperation(),
                    aircraft.getCoverageType(),
                    aircraft.getHomeAirportsCode(),
                    aircraft.getHomeAirportsLocationAddress(),
                    formatConfidenceScore(aircraft.getConfidence())
                ))
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("Error transforming aircraft info: ", e);
            return new ArrayList<>();
        }
    }

    private List<AdditionalInsuredInfoReportModel> transformAdditionalInsuredInfo(SubmissionRequestDTO submissionRequestDTO) {
        try {
            if (submissionRequestDTO.getSubmissionBody() == null ||
                submissionRequestDTO.getSubmissionBody().getBody() == null ||
                submissionRequestDTO.getSubmissionBody().getBody().getAdditionalInsuredInfo() == null) {
                return new ArrayList<>();
            }

            var additionalInsuredInfoList = submissionRequestDTO.getSubmissionBody().getBody().getAdditionalInsuredInfo();

            return additionalInsuredInfoList.stream()
                .map(additionalInsured -> new AdditionalInsuredInfoReportModel(
                    additionalInsured.getName(),
                    additionalInsured.getAddress(),
                    additionalInsured.getCity(),
                    additionalInsured.getState(),
                    additionalInsured.getCountry(),
                    additionalInsured.getZip(),
                    additionalInsured.getInsuredInterest(),
                    formatConfidenceScore(additionalInsured.getConfidence())
                ))
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("Error transforming additional insured info: ", e);
            return new ArrayList<>();
        }
    }

    private String generateSubmissionExtractFileName(SubmissionRequestDTO submissionRequestDTO, String reportId) {
        String submissionId = submissionRequestDTO.getSubmissionId() != null ? submissionRequestDTO.getSubmissionId() : "unknown";
        return String.format("WHITESPACE_SUBMISSION_%s.xlsx", submissionId);
    }

    /**
     * Generates a file name based on the type of report requested and additional parameters.
     *
     * @param reportRequestDTO The DTO containing information about the report request.
     * @param reportId         The ID of the report.
     * @return The generated file name.
     */
    private String generateFileNameByReportType(ReportRequestDTO reportRequestDTO, String reportId) {

        ReportingType reportingType = reportRequestDTO.getReportingType();
        String fileName = reportingType.name() + "_report";

        fileName = switch (reportingType) {
            case BORDEREAU, PAYABLE_CARRIER, INVOICE_DETAIL, ACCOUNT_RECEIVABLE_AGING, AVIATION_BORDEREAU,
                 AVIATION_SUBMISSION ->
                    fileName + "_" + reportRequestDTO.getStartDate() + "_to_" + reportRequestDTO.getEndDate() + "_" + reportId + ".xlsx";
            case ASU_AGING, ALL_DATA, WHITESPACE_SUBMISSION -> fileName + "_" + reportId + ".xlsx";
            // Add cases for other ReportingTypes if necessary
        };
        return fileName;
    }

    /**
     * Filters endorsements up to the last "REWRITE" endorsement within the same month and year
     * as the policy bound date for each policy in the provided map.
     *
     * <p>This method iterates through the endorsements for each policy, sorts them by creation date,
     * and identifies the most recent "REWRITE" endorsement where the effective date's month and year
     * match the policy bound date's month and year. After identifying the matching rewrite, it retains
     * all endorsements from that point onward.</p>
     *
     * @param endorsementsMap A map where the key is the policy ID, and the value is a list of endorsements.
     *                         Each endorsement is represented as a MongoDB Document.
     * @return A map containing the filtered endorsements for each policy, with all endorsements from
     *         the last matching "REWRITE" onwards.
     */
    private Map<String, List<Document>> filterEndorsementsUpToLastCancelRewriteInPolicyBoundMonth(Map<String, List<Document>> endorsementsMap) {

        for (Map.Entry<String, List<Document>> entry : endorsementsMap.entrySet()) {
            String policyId = entry.getKey();
            List<Document> endorsementList = entry.getValue();

            // Sort endorsements by creation date (Ascending)
            endorsementList.sort(Comparator.comparing(doc -> doc.getDate(CREATED_AT)));

            int lastMatchingRewriteIndex = -1;

            // Iterate once (forward) to find the last "REWRITE" endorsement in the same policy bound month
            for (int i = 0; i < endorsementList.size(); i++) {
                Document endorsement = endorsementList.get(i);

                if ("REWRITE".equalsIgnoreCase(endorsement.getString(ENDORSEMENT_TYPE))) {
                    Document updatedPolicyInfo = (Document) endorsement.get(UPDATED_POLICY_INFO);
                    String policyBoundDateObject = updatedPolicyInfo.get(POLICY_BOUND_DATE, String.class);
                    LocalDate policyBoundDate = LocalDate.parse(policyBoundDateObject);

                    log.info("Policy bound date for endorsement : {}, for policyId : {}", policyBoundDate, policyId);

                    LocalDate endorsementCreatedDate = endorsement.getDate(CREATED_AT)
                            .toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

                    log.info("Endorsement created date : {}, for policyId : {}", endorsementCreatedDate, policyId);

                    // Compare month and year
                    if (policyBoundDate.getMonth() == endorsementCreatedDate.getMonth() && policyBoundDate.getYear() == endorsementCreatedDate.getYear()) {
                        lastMatchingRewriteIndex = i;
                        log.info("PolicyId: {}, REWRITE endorsement found in same policy bound month at Index: {} with Created Date: {}", policyId, lastMatchingRewriteIndex, endorsementCreatedDate);
                    }
                }
            }

            // Validate the found index
            if (lastMatchingRewriteIndex == -1) {
                log.warn("No matching REWRITE endorsement found for policyId: {}", policyId);
            }else{
                // keep endorsements from the last matching "REWRITE" onwards (inclusive)
                endorsementList = endorsementList.subList(lastMatchingRewriteIndex, endorsementList.size());
                log.info("PolicyId: {}, Found {} endorsements from last matching REWRITE onwards.", policyId, endorsementList.size());

                // store updated list
                endorsementsMap.put(policyId, endorsementList);
            }
        }

        return endorsementsMap;
    }
}
