package com.concirrus.reporting.service.impl;

import com.concirrus.reporting.dto.AlertDTO;
import com.concirrus.reporting.service.AlertService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class SlackAlertServiceImpl implements AlertService {
    private final RestTemplate restTemplate;

    public SlackAlertServiceImpl(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Override
    public void postAlert(String url, AlertDTO alertPayload) {
        try {
            if (url != null && !url.isEmpty()) {
                restTemplate.postForEntity(url, alertPayload, Void.class);
                log.info("Successfully sent alert to Slack: {}", alertPayload.getText());
            } else {
                log.warn("Slack alert URL not configured. Alert message: {}", alertPayload.getText());
            }
        } catch (Exception e) {
            log.error("Failed to send alert to Slack. Message: {}, Error: {}", alertPayload.getText(), e.getMessage());
        }
    }
}