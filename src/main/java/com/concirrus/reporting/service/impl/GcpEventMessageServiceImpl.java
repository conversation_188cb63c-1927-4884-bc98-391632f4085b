package com.concirrus.reporting.service.impl;

import com.concirrus.reporting.service.IEventMessageService;
import com.google.cloud.pubsub.v1.Publisher;
import com.google.protobuf.ByteString;
import com.google.pubsub.v1.PubsubMessage;
import com.google.pubsub.v1.TopicName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Component
@ConditionalOnProperty(value = "cloud.provider", havingValue = "gcp")
public class GcpEventMessageServiceImpl implements IEventMessageService {
    private static final Logger logger = LoggerFactory.getLogger(GcpEventMessageServiceImpl.class);

    private final String gcpProjectId;

    public GcpEventMessageServiceImpl(@Value("${cloud.gcp.project-id}") String gcpProjectId) {
        this.gcpProjectId = gcpProjectId;
    }

    @Override
    public void publishMessage(String destinationChannel, String message) {
        logger.info("Sending the message to {}: {}", destinationChannel, message);

        TopicName topicName = TopicName.of(gcpProjectId, destinationChannel);
        Publisher publisher = null;
        try {
            // Create a publisher instance with default settings bound to the topic
            publisher = Publisher.newBuilder(topicName).build();

            PubsubMessage pubsubMessage = PubsubMessage.newBuilder()
                    .setData(ByteString.copyFromUtf8(message))
                    .build();

            // Once published, returns a server-assigned message id (unique within the topic)
            String messageId = publisher.publish(pubsubMessage).get(1, TimeUnit.MINUTES);
            logger.info("Published message ID: {}", messageId);
        } catch (IOException | ExecutionException | InterruptedException | TimeoutException e) {
            logger.error("Error pushing message to topic {}", destinationChannel, e);
            throw new RuntimeException(e);
        } finally {
            if (Objects.nonNull(publisher)) {
                // When finished with the publisher, shutdown to free up resources.
                publisher.shutdown();
            }
        }
        logger.info("Successfully published to {} queue", destinationChannel);
    }
}