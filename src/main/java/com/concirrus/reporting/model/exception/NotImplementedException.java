package com.concirrus.reporting.model.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.io.Serial;

/**
 * <AUTHOR> mohit
 * @since : Jan 2024
 */
@ResponseStatus(value = HttpStatus.NOT_IMPLEMENTED, reason = "This functionality is not implemented yet")
public class NotImplementedException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 3963189798593985281L;

    private static final String DEFAULT_MESSAGE = "This functionality is not implemented yet";

    public NotImplementedException(String message) {
        super(message);
    }

    public NotImplementedException() {
        super(DEFAULT_MESSAGE);
    }

    public NotImplementedException(Throwable cause) {
        super(cause);
    }
}
