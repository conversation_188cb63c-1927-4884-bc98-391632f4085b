package com.concirrus.reporting.model.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.io.Serial;

/**
 * <AUTHOR> mohit
 * @since : Jan 2024
 */
@ResponseStatus(value = HttpStatus.UNAUTHORIZED)
public class NotOwnerException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = -4550654460660374327L;

    private static final String DEFAULT_MESSAGE = "You are not authorized to access this resources";

    public NotOwnerException(String message) {
        super(message);
    }

    public NotOwnerException() {
        super(DEFAULT_MESSAGE);
    }

    public NotOwnerException(Throwable cause) {
        super(cause);
    }
}
