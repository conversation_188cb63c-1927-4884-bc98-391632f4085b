package com.concirrus.reporting.model.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.io.Serial;

/**
 * <AUTHOR> mohit
 * @since : Jan 2024
 */
@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
public class ApplicationException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 3749138303937568590L;

    private static final String DEFAULT_MESSAGE = "Application Exception";

    public ApplicationException() {
        super(DEFAULT_MESSAGE);
    }

    public ApplicationException(String message) {
        super(message);
    }
}
