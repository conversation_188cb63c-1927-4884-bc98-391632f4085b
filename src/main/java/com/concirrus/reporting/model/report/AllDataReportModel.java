package com.concirrus.reporting.model.report;

import com.concirrus.reporting.annotation.XlsxSheet;
import com.concirrus.reporting.annotation.XlsxSingleField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
@XlsxSheet(value = "AllData")
public class AllDataReportModel {

    @XlsxSingleField(columnIndex = 0, label = "Transaction Type")
    private Object transactionType;

    @XlsxSingleField(columnIndex = 1, label = "Status")
    private Object status;

    @XlsxSingleField(columnIndex = 2, label = "Defined Status")
    private Object definedStatus;

    @XlsxSingleField(columnIndex = 3, label = "Policy Inception")
    private Object policyInception;

    @XlsxSingleField(columnIndex = 4, label = "Policy Expiration")
    private Object policyExpiration;

    @XlsxSingleField(columnIndex = 5, label = "Underwriter")
    private Object underwriter;

    @XlsxSingleField(columnIndex = 6, label = "Underwriter Email")
    private Object underwriterEmail;

    @XlsxSingleField(columnIndex = 7, label = "Transaction/Process Date (Received/Submitted Date)")
    private Object transactionProcessDate;

    @XlsxSingleField(columnIndex = 8, label = "Submission/Customer Number")
    private Object submissionCustomerNumber;

    @XlsxSingleField(columnIndex = 9, label = "Prior Policy #")
    private Object priorPolicyNumber;

    @XlsxSingleField(columnIndex = 10, label = "Broker Name (Brokerage/Company)")
    private Object brokerName;

    @XlsxSingleField(columnIndex = 11, label = "Broker Team")
    private Object brokerTeam;

    @XlsxSingleField(columnIndex = 12, label = "Broker Name (Sender)")
    private Object brokerSenderName;

    @XlsxSingleField(columnIndex = 13, label = "Broker Email")
    private Object brokerEmail;

    @XlsxSingleField(columnIndex = 14, label = "Broker Mailing Street Address")
    private Object brokerMailingStreetAddress;

    @XlsxSingleField(columnIndex = 15, label = "Broker Mailing City")
    private Object brokerMailingCity;

    @XlsxSingleField(columnIndex = 16, label = "Broker Mailing State")
    private Object brokerMailingState;

    @XlsxSingleField(columnIndex = 17, label = "Broker Mailing Zip Code")
    private Object brokerMailingZipCode;

    @XlsxSingleField(columnIndex = 18, label = "First Named Insured")
    private Object firstNamedInsured;

    @XlsxSingleField(columnIndex = 19, label = "DBA")
    private Object dba;

    @XlsxSingleField(columnIndex = 20, label = "Other Named Insured(s)")
    private Object otherNamedInsureds;

    @XlsxSingleField(columnIndex = 21, label = "Named Insured - Owner")
    private Object namedInsuredOwner;

    @XlsxSingleField(columnIndex = 22, label = "Named Insured - General Contractor/Construction Manager")
    private Object namedInsuredGC;

    @XlsxSingleField(columnIndex = 23, label = "Mailing Street Address 1")
    private Object mailingStreetAddress1;

    @XlsxSingleField(columnIndex = 24, label = "Mailing Street Address 2")
    private Object mailingStreetAddress2;

    @XlsxSingleField(columnIndex = 25, label = "Mailing City")
    private Object mailingCity;

    @XlsxSingleField(columnIndex = 26, label = "Mailing State")
    private Object mailingState;

    @XlsxSingleField(columnIndex = 27, label = "Mailing Zip Code")
    private Object mailingZipCode;

    @XlsxSingleField(columnIndex = 28, label = "Description of Operations/Project")
    private Object descriptionOfOperations;

    @XlsxSingleField(columnIndex = 29, label = "Product Type")
    private Object productType;

    @XlsxSingleField(columnIndex = 30, label = "Schedule of Values")
    private Object scheduleOfValues;

    @XlsxSingleField(columnIndex = 31, label = "Project Name")
    private Object projectName;

    @XlsxSingleField(columnIndex = 32, label = "Project Street Address")
    private Object projectStreetAddress;

    @XlsxSingleField(columnIndex = 33, label = "Project City")
    private Object projectCity;

    @XlsxSingleField(columnIndex = 34, label = "Project State")
    private Object projectState;

    @XlsxSingleField(columnIndex = 35, label = "Project Zip Code")
    private Object projectZipCode;

    @XlsxSingleField(columnIndex = 36, label = "Carrier")
    private Object carrier;

    @XlsxSingleField(columnIndex = 37, label = "Risk State")
    private Object riskState;

    @XlsxSingleField(columnIndex = 38, label = "Each Occurrence Limit")
    private Object eachOccurrenceLimit;

    @XlsxSingleField(columnIndex = 39, label = "General Aggregate Limit (other than Products/Completed Operations)")
    private Object generalAggregateLimit;

    @XlsxSingleField(columnIndex = 40, label = "Products/Completed Operations Aggregate Limit")
    private Object productsCompletedOpsAggregateLimit;

    @XlsxSingleField(columnIndex = 41, label = "Personal and Advertising Injury Limit")
    private Object personalAdvertisingInjuryLimit;

    @XlsxSingleField(columnIndex = 42, label = "Damage to Premises Rented to You")
    private Object damageToPremisesRented;

    @XlsxSingleField(columnIndex = 43, label = "Medical Payments Limit")
    private Object medicalPaymentsLimit;

    @XlsxSingleField(columnIndex = 44, label = "Other Aggregate Limit")
    private Object otherAggregateLimit;

    @XlsxSingleField(columnIndex = 45, label = "Products-Completed Operations Aggregate")
    private Object productsCompletedOpsAggregate;

    @XlsxSingleField(columnIndex = 46, label = "Policy Aggregate Limit (auto)")
    private Object policyAggregateLimit;

    @XlsxSingleField(columnIndex = 47, label = "Retention Type")
    private Object retentionType;

    @XlsxSingleField(columnIndex = 48, label = "Deductible Amount")
    private Object deductibleAmount;

    @XlsxSingleField(columnIndex = 49, label = "SIR Amount")
    private Object sirAmount;

    @XlsxSingleField(columnIndex = 50, label = "# of General Aggregate Reinstatements")
    private Object generalAggregateReinstatements;

    @XlsxSingleField(columnIndex = 51, label = "Rate by Exposure Basis")
    private Object rateByExposureBasis;

    @XlsxSingleField(columnIndex = 52, label = "ISO Classification Code")
    private Object isoClassificationCode;

    @XlsxSingleField(columnIndex = 53, label = "Exposure Basis")
    private Object exposureBasis;

    @XlsxSingleField(columnIndex = 54, label = "Exposure Basis Detailed")
    private Object exposureBasisDetailed;

    @XlsxSingleField(columnIndex = 55, label = "Exposure Amount by Exposure Basis")
    private Object exposureAmountByExposureBasis;

    @XlsxSingleField(columnIndex = 56, label = "Fee Description")
    private Object feeDescription;

    @XlsxSingleField(columnIndex = 57, label = "Fee Amounts")
    private Object feeAmounts;

    @XlsxSingleField(columnIndex = 58, label = "Total Written Premium")
    private Object totalWrittenPremium;

    @XlsxSingleField(columnIndex = 59, label = "TRIA Premium")
    private Object triaPremium;

    @XlsxSingleField(columnIndex = 60, label = "Total # of Autos")
    private Object totalNumberOfAutos;

    @XlsxSingleField(columnIndex = 61, label = "Audit")
    private Object audit;

    @XlsxSingleField(columnIndex = 62, label = "Commission %")
    private Object commissionPercentage;

    @XlsxSingleField(columnIndex = 63, label = "Commission $")
    private Object commissionAmount;

    @XlsxSingleField(columnIndex = 64, label = "Total Technical Premium")
    private Object totalTechnicalPremium;

    @XlsxSingleField(columnIndex = 65, label = "Sold to Technical %")
    private Object soldToTechnicalPercentage;

    @XlsxSingleField(columnIndex = 66, label = "Underwriter Debit/Credit Factor")
    private Object underwriterDebitCreditFactor;

    @XlsxSingleField(columnIndex = 67, label = "NYFTZ Class")
    private Object nyftzClass;

    @XlsxSingleField(columnIndex = 68, label = "Endorsement Effective Date")
    private Object endorsementEffectiveDate;

    @XlsxSingleField(columnIndex = 69, label = "Endorsement Transaction Description")
    private Object endorsementTransactionDescription;

    @XlsxSingleField(columnIndex = 70, label = "Endorsement Number")
    private Object endorsementNumber;

    @XlsxSingleField(columnIndex = 71, label = "Endorsement Sequence")
    private Object endorsementSequence;

    @XlsxSingleField(columnIndex = 72, label = "Invoice #")
    private Object invoiceNumber;

    @XlsxSingleField(columnIndex = 73, label = "Invoice Due Date")
    private Object invoiceDueDate;

}
