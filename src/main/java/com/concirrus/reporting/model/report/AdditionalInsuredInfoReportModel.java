package com.concirrus.reporting.model.report;

import com.concirrus.reporting.annotation.XlsxSheet;
import com.concirrus.reporting.annotation.XlsxSingleField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
@XlsxSheet(value = "Additional Insured Information")
public class AdditionalInsuredInfoReportModel {

    @XlsxSingleField(columnIndex = 0, label = "Name")
    private String name;

    @XlsxSingleField(columnIndex = 1, label = "Address")
    private String address;

    @XlsxSingleField(columnIndex = 2, label = "City")
    private String city;

    @XlsxSingleField(columnIndex = 3, label = "State")
    private String state;

    @XlsxSingleField(columnIndex = 4, label = "Country")
    private String country;

    @XlsxSingleField(columnIndex = 5, label = "ZIP Code")
    private String zip;

    @XlsxSingleField(columnIndex = 6, label = "Insured Interest")
    private String insuredInterest;

    @XlsxSingleField(columnIndex = 7, label = "Confidence Score (%)")
    private String confidence;
}