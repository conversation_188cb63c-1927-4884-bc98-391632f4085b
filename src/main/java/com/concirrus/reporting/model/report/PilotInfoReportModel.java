package com.concirrus.reporting.model.report;

import com.concirrus.reporting.annotation.XlsxSheet;
import com.concirrus.reporting.annotation.XlsxSingleField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
@XlsxSheet(value = "Pilot Information")
public class PilotInfoReportModel {

    @XlsxSingleField(columnIndex = 0, label = "Name")
    private String name;

    @XlsxSingleField(columnIndex = 1, label = "Date of Birth")
    private String dob;

    @XlsxSingleField(columnIndex = 2, label = "Age")
    private String age;

    @XlsxSingleField(columnIndex = 3, label = "Training Completion Date")
    private String dateOfCompletion;

    @XlsxSingleField(columnIndex = 4, label = "Training Facility")
    private String trainingFacility;

    @XlsxSingleField(columnIndex = 5, label = "Total Hours")
    private String totalHours;

    @XlsxSingleField(columnIndex = 6, label = "Multi-Engine Hours")
    private String pilotFlightHours_me;

    @XlsxSingleField(columnIndex = 7, label = "Single-Engine Hours")
    private String pilotFlightHours_se;

    @XlsxSingleField(columnIndex = 8, label = "Multi-Modal Hours")
    private String pilotFlightHours_mm;

    @XlsxSingleField(columnIndex = 9, label = "Fixed Wing Hours")
    private String pilotFlightHours_fw;

    @XlsxSingleField(columnIndex = 10, label = "Rotary Wing Hours")
    private String pilotFlightHours_rw;

    @XlsxSingleField(columnIndex = 11, label = "Retractable Gear Hours")
    private String pilotFlightHours_rg;

    @XlsxSingleField(columnIndex = 12, label = "Turbo Prop Hours")
    private String pilotFlightHours_tp;

    @XlsxSingleField(columnIndex = 13, label = "Turbo Jet Hours")
    private String pilotFlightHours_tj;

    @XlsxSingleField(columnIndex = 14, label = "Confidence Score")
    private String confidence;
}