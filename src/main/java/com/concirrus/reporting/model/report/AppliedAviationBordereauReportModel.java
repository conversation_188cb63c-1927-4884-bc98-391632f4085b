package com.concirrus.reporting.model.report;

import com.concirrus.reporting.annotation.XlsxSheet;
import com.concirrus.reporting.annotation.XlsxSingleField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
@XlsxSheet(value = "Bordereau")
public class AppliedAviationBordereauReportModel {

    @XlsxSingleField(columnIndex = 0, label = "Reporting month")
    private Object reportingMonth;

    @XlsxSingleField(columnIndex = 1, label = "Underwriter")
    private Object underwriter;

    @XlsxSingleField(columnIndex = 2, label = "Transaction type")
    private Object transactionType;

    @XlsxSingleField(columnIndex = 3, label = "Account Name / Insured")
    private Object insuredName;

    @XlsxSingleField(columnIndex = 4, label = "Policy State")
    private Object policyState;

    @XlsxSingleField(columnIndex = 5, label = "Policy Country")
    private Object policyCountry;

    @XlsxSingleField(columnIndex = 6, label = "Mailing Street Address")
    private Object mailingStreetAddress;

    @XlsxSingleField(columnIndex = 7, label = "Mailing City")
    private Object mailingCity;

    @XlsxSingleField(columnIndex = 8, label = "Mailing State")
    private Object mailingState;

    @XlsxSingleField(columnIndex = 9, label = "Mailing Zip")
    private Object mailingZip;

    @XlsxSingleField(columnIndex = 10, label = "Mailing Country")
    private Object mailingCountry;

    @XlsxSingleField(columnIndex = 11, label = "Endorsement Number")
    private Object endorsementNumber;

    @XlsxSingleField(columnIndex = 12, label = "Policy Number")
    private Object policyNumber;

    @XlsxSingleField(columnIndex = 13, label = "Transaction Date")
    private Object transactionDate;

    @XlsxSingleField(columnIndex = 14, label = "Endorsement Effective Date")
    private Object endorsementEffectiveDate;

    @XlsxSingleField(columnIndex = 15, label = "Policy Effective Date")
    private Object policyEffectiveDate;

    @XlsxSingleField(columnIndex = 16, label = "Expiration Date")
    private Object policyExpirationDate;

    @XlsxSingleField(columnIndex = 17, label = "Quote")
    private Object quoteDate;

    @XlsxSingleField(columnIndex = 18, label = "Bound")
    private Object boundDate;

    @XlsxSingleField(columnIndex = 19, label = "Issued")
    private Object issuedDate;

    @XlsxSingleField(columnIndex = 20, label = "Carrier")
    private Object carrier;

    @XlsxSingleField(columnIndex = 21, label = "Broker ID")
    private Object brokerId;

    @XlsxSingleField(columnIndex = 22, label = "Broker Name")
    private Object brokerName;

    @XlsxSingleField(columnIndex = 23, label = "Broker Address")
    private Object brokerAddress;

    @XlsxSingleField(columnIndex = 24, label = "Broker City")
    private Object brokerCity;

    @XlsxSingleField(columnIndex = 25, label = "Broker State")
    private Object brokerState;

    @XlsxSingleField(columnIndex = 26, label = "Broker Zipcode")
    private Object brokerZipcode;

    @XlsxSingleField(columnIndex = 27, label = "Broker Country")
    private Object brokerCountry;

    @XlsxSingleField(columnIndex = 28, label = "Broker Commission Percentage")
    private Object brokerCommissionPercentage;

    @XlsxSingleField(columnIndex = 29, label = "Lead / Follow")
    private Object leadOrFollow;

    @XlsxSingleField(columnIndex = 30, label = "Quota Share Percentage")
    private Object quotaSharePercentage;

    @XlsxSingleField(columnIndex = 31, label = "Product Line")
    private Object productLine;

    @XlsxSingleField(columnIndex = 32, label = "Aviation Segments")
    private Object aviationSegments;

    @XlsxSingleField(columnIndex = 33, label = "Annual Statement Line (ASL)")
    private Object annualStatementLine;

    @XlsxSingleField(columnIndex = 34, label = "No. of A/C")
    private Object numberOfAircraft;

    @XlsxSingleField(columnIndex = 35, label = "100% Lead Liability Limit")
    private Object leadLiabilityLimit;

    @XlsxSingleField(columnIndex = 36, label = "Applied Liability Limit")
    private Object appliedLiabilityLimit;

    @XlsxSingleField(columnIndex = 37, label = "Underlying Limit")
    private Object underlyingLimit;

    @XlsxSingleField(columnIndex = 38, label = "100% Aggregate limit")
    private Object aggregateLimit;

    @XlsxSingleField(columnIndex = 39, label = "Applied Aggregate Limit")
    private Object appliedAggregateLimit;

    @XlsxSingleField(columnIndex = 40, label = "Max Hull Value")
    private Object maxHullValue;

    @XlsxSingleField(columnIndex = 41, label = "Applied Max Hull Value")
    private Object appliedMaxHullValue;

    @XlsxSingleField(columnIndex = 42, label = "100% Total Hull Value")
    private Object totalHullValue;

    @XlsxSingleField(columnIndex = 43, label = "Applied Total Hull Value")
    private Object appliedTotalHullValue;

    @XlsxSingleField(columnIndex = 44, label = "Applied Liability Premium")
    private Object appliedLiabilityPremium;

    @XlsxSingleField(columnIndex = 45, label = "Applied Hull Premium")
    private Object appliedHullPremium;

    @XlsxSingleField(columnIndex = 46, label = "Does Policy cover War Liab Premium?")
    private Object coversWarLiabPremium;

    @XlsxSingleField(columnIndex = 47, label = "Applied War Liab Premium")
    private Object appliedWarLiabPremium;

    @XlsxSingleField(columnIndex = 48, label = "Does Policy cover Excess War")
    private Object coversExcessWar;

    @XlsxSingleField(columnIndex = 49, label = "Applied Excess War Premium")
    private Object appliedExcessWarPremium;

    @XlsxSingleField(columnIndex = 50, label = "Does Policy cover Hull War?")
    private Object coversHullWar;

    @XlsxSingleField(columnIndex = 51, label = "Applied Hull War Premium")
    private Object appliedHullWarPremium;

    @XlsxSingleField(columnIndex = 52, label = "Does Policy Cover TRIA Liability?")
    private Object coversTriaLiability;

    @XlsxSingleField(columnIndex = 53, label = "TRIA Liability Premium")
    private Object triaLiabilityPremium;

    @XlsxSingleField(columnIndex = 54, label = "Does Policy Cover TRIA Hull?")
    private Object coversTriaHull;

    @XlsxSingleField(columnIndex = 55, label = "TRIA Hull Premium")
    private Object triaHullPremium;

    @XlsxSingleField(columnIndex = 56, label = "Currency")
    private Object currency;

    @XlsxSingleField(columnIndex = 57, label = "Applied Aviation Commission Percentage")
    private Object appliedAviationCommissionPercentage;

    @XlsxSingleField(columnIndex = 58, label = "Applied Aviation Commission")
    private Object appliedAviationCommission;

    @XlsxSingleField(columnIndex = 59, label = "Total Broker Plus Applied Commission Amount")
    private Object totalBrokerPlusCommission;

    @XlsxSingleField(columnIndex = 60, label = "Bursary Fees")
    private Object bursaryFees;

    @XlsxSingleField(columnIndex = 61, label = "Lead Fees")
    private Object leadFees;

    @XlsxSingleField(columnIndex = 62, label = "Mexican Certificate Pass Through Fee")
    private Object mexicanPassThroughFee;

    @XlsxSingleField(columnIndex = 63, label = "Mexican Certificate Pass Through premium")
    private Object mexicanPassThroughPremium;

    @XlsxSingleField(columnIndex = 64, label = "Mexican Certificate Pass Through commission")
    private Object mexicanPassThroughCommission;

    @XlsxSingleField(columnIndex = 65, label = "State Surcharges description")
    private Object stateSurchargesDescription;

    @XlsxSingleField(columnIndex = 66, label = "State Surcharges")
    private Object stateSurcharges;

    @XlsxSingleField(columnIndex = 67, label = "Municipal Surcharges description")
    private Object municipalSurchargesDescription;

    @XlsxSingleField(columnIndex = 68, label = "Municipal Surcharges")
    private Object municipalSurcharges;

    @XlsxSingleField(columnIndex = 69, label = "Applied Total Gross Premium")
    private Object appliedTotalGrossPremium;

    @XlsxSingleField(columnIndex = 70, label = "Applied Total Gross Premium plus Taxes and Fees")
    private Object appliedGrossPremiumWithTaxes;

    @XlsxSingleField(columnIndex = 71, label = "Total Net Premium")
    private Object totalNetPremium;

    @XlsxSingleField(columnIndex = 72, label = "Total Net Premium plus Taxes and Fees")
    private Object netPremiumWithTaxes;

    @XlsxSingleField(columnIndex = 73, label = "Broker Comission Due")
    private Object brokerCommissionDue;

    @XlsxSingleField(columnIndex = 74, label = "Total Due To Carrier")
    private Object totalDueToCarrier;

    @XlsxSingleField(columnIndex = 75, label = "Invoice Number")
    private Object invoiceNumber;

    @XlsxSingleField(columnIndex = 76, label = "Invoice Date")
    private Object invoiceDate;

    @XlsxSingleField(columnIndex = 77, label = "Payment Plan")
    private Object paymentPlan;

    @XlsxSingleField(columnIndex = 78, label = "Earned Premium")
    private Object earnedPremium;

    @XlsxSingleField(columnIndex = 79, label = "Unearned Premium")
    private Object unearnedPremium;

    @XlsxSingleField(columnIndex = 80, label = "Earned Commission")
    private Object earnedCommission;

    @XlsxSingleField(columnIndex = 81, label = "Unearned Commission")
    private Object unearnedCommission;

    @XlsxSingleField(columnIndex = 82, label = "Earned Commission 90%")
    private Object earnedCommission90;

    @XlsxSingleField(columnIndex = 83, label = "Unearned Commission 90%")
    private Object unearnedCommission90;

    @XlsxSingleField(columnIndex = 84, label = "Does Fac Exist on this policy?")
    private Object facExists;

    @XlsxSingleField(columnIndex = 85, label = "Unique Row ID")
    private Object uniqueRowId;

    @XlsxSingleField(columnIndex = 86, label = "Broker Report Helper Column")
    private Object brokerReportHelper;
}

