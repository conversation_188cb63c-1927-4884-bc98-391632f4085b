package com.concirrus.reporting.model.report;

import com.concirrus.reporting.annotation.XlsxSheet;
import com.concirrus.reporting.annotation.XlsxSingleField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
@XlsxSheet(value = "Sheet1")
public class InvoiceDetailReportModel {

    @XlsxSingleField(columnIndex = 0, label = "Policy Number")
    private Object policyNumber;

    @XlsxSingleField(columnIndex = 1, label = "Name")
    private Object insuredName;

    @XlsxSingleField(columnIndex = 2, label = "Invoice Number")
    private Object invoiceNumber;

    @XlsxSingleField(columnIndex = 3, label = "Invoice Date")
    private Object invoiceBilledDate;

    @XlsxSingleField(columnIndex = 4, label = "Invoice Amount")
    private Object invoiceTotalDue;

    @XlsxSingleField(columnIndex = 5, label = "Premium")
    private Object premium;

    @XlsxSingleField(columnIndex = 6, label = "Taxes")
    private Object taxAmount;

    @XlsxSingleField(columnIndex = 7, label = "Fees")
    private Object totalFees;

    @XlsxSingleField(columnIndex = 8, label = "Agent Commission")
    private Object commission;

}
