package com.concirrus.reporting.model.report;

import com.concirrus.reporting.annotation.XlsxSheet;
import com.concirrus.reporting.annotation.XlsxSingleField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
@XlsxSheet(value = "Risk Details")
public class RiskDetailsWhitespaceReportModel {

    @XlsxSingleField(columnIndex = 0, label = "Field Name (Whitespace)")
    private String fieldName;

    @XlsxSingleField(columnIndex = 1, label = "Mapped Value")
    private String mappedValue;

    @XlsxSingleField(columnIndex = 2, label = "Confidence Score (%)")
    private String confidenceScore;
}