package com.concirrus.reporting.model.report;

import com.concirrus.reporting.annotation.XlsxSheet;
import com.concirrus.reporting.annotation.XlsxSingleField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
@XlsxSheet(value = "Detailed")
public class AppliedAviationSubmissionDetailedReportModel {

    @XlsxSingleField(columnIndex = 0, label = "submission#")
    private Object submissionNumber;

    @XlsxSingleField(columnIndex = 1, label = "received Date")
    private Object receivedDate;

    @XlsxSingleField(columnIndex = 2, label = "Proposed Effective Date")
    private Object proposedEffectiveDate;

    @XlsxSingleField(columnIndex = 3, label = "Submission Status")
    private Object submissionStatus;

    @XlsxSingleField(columnIndex = 4, label = "Underwriter Name")
    private Object underwriterName;

    @XlsxSingleField(columnIndex = 5, label = "Name Insured")
    private Object nameInsured;

    @XlsxSingleField(columnIndex = 6, label = "Name Insured Address")
    private Object nameInsuredAddress;

    @XlsxSingleField(columnIndex = 7, label = "Name Insured City")
    private Object nameInsuredCity;

    @XlsxSingleField(columnIndex = 8, label = "Name Insured State")
    private Object nameInsuredState;

    @XlsxSingleField(columnIndex = 9, label = "Name Insured Zip")
    private Object nameInsuredZip;

    @XlsxSingleField(columnIndex = 10, label = "Line Of Business")
    private Object lineOfBusiness;

    @XlsxSingleField(columnIndex = 11, label = "Product")
    private Object product;

    @XlsxSingleField(columnIndex = 12, label = "Aircraft N#")
    private Object aircraftNumber;

    @XlsxSingleField(columnIndex = 13, label = "Aircraft Year")
    private Object aircraftYear;

    @XlsxSingleField(columnIndex = 14, label = "Aircraft Make")
    private Object aircraftMake;

    @XlsxSingleField(columnIndex = 15, label = "Aircraft Model")
    private Object aircraftModel;

    @XlsxSingleField(columnIndex = 16, label = "Crew Seats")
    private Object crewSeats;

    @XlsxSingleField(columnIndex = 17, label = "Pass Seats")
    private Object passengerSeats;

    @XlsxSingleField(columnIndex = 18, label = "Hull Value")
    private Object hullValue;

    @XlsxSingleField(columnIndex = 19, label = "Im Deductible")
    private Object imDeductible;

    @XlsxSingleField(columnIndex = 20, label = "Nim Deductible")
    private Object nimDeductible;

    @XlsxSingleField(columnIndex = 21, label = "Requested Limit")
    private Object requestedLimit;

    @XlsxSingleField(columnIndex = 22, label = "Coverage Type")
    private Object coverageType;

    @XlsxSingleField(columnIndex = 23, label = "Producer Contact Name")
    private Object producerContactName;

    @XlsxSingleField(columnIndex = 24, label = "Agency Name")
    private Object agencyName;

    @XlsxSingleField(columnIndex = 25, label = "Agency Address")
    private Object agencyAddress;

    @XlsxSingleField(columnIndex = 26, label = "Agency City")
    private Object agencyCity;

    @XlsxSingleField(columnIndex = 27, label = "Agency State")
    private Object agencyState;

    @XlsxSingleField(columnIndex = 28, label = "Agency Zip")
    private Object agencyZip;

    @XlsxSingleField(columnIndex = 29, label = "Decline Reason")
    private Object declineReason;
}