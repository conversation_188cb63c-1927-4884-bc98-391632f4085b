package com.concirrus.reporting.model.report;

import com.concirrus.reporting.annotation.XlsxSheet;
import com.concirrus.reporting.annotation.XlsxSingleField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
@XlsxSheet(value = "AccountReceivableAging")
public class AccountReceivableAgingReportModel {

    @XlsxSingleField(columnIndex = 0, label = "Policy Number")
    private Object policyNumber;

    @XlsxSingleField(columnIndex = 1, label = "Name Insured")
    private Object customerName;

    @XlsxSingleField(columnIndex = 2, label = "Invoice Number")
    private Object invoiceNumber;

    @XlsxSingleField(columnIndex = 3, label = "Invoice Date")
    private Object invoiceDate;

    @XlsxSingleField(columnIndex = 4, label = "Balance (Total Due)")
    private Object balance;

    @XlsxSingleField(columnIndex = 5, label = "Current")
    private Object current;

    @XlsxSingleField(columnIndex = 6, label = "30 Days")
    private Object thirtyDayNotice;

    @XlsxSingleField(columnIndex = 7, label = "60 Days")
    private Object sixtyDayNotice;

    @XlsxSingleField(columnIndex = 8, label = "90 Days")
    private Object ninetyDayNotice;

    @XlsxSingleField(columnIndex = 9, label = "120 Days")
    private Object hundredTwentyDays;
}
