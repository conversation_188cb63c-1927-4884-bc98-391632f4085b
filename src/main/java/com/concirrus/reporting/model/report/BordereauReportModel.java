package com.concirrus.reporting.model.report;

import com.concirrus.reporting.annotation.XlsxSheet;
import com.concirrus.reporting.annotation.XlsxSingleField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
@XlsxSheet(value = "Bordereau")
public class BordereauReportModel {

    @XlsxSingleField(columnIndex = 0, label = "Accounting Month")
    private Object accountingMonth;

    @XlsxSingleField(columnIndex = 1, label = "Policy #")
    private Object policyNumber;

    @XlsxSingleField(columnIndex = 2, label = "Status")
    private Object status;

    @XlsxSingleField(columnIndex = 3, label = "Named Insured")
    private Object insuredName;

    @XlsxSingleField(columnIndex = 4, label = "Effective Date")
    private Object effectiveDate;

    @XlsxSingleField(columnIndex = 5, label = "Expiration Date")
    private Object expiryDate;

    @XlsxSingleField(columnIndex = 6, label = "Broker Name (Brokerage/Company)")
    private Object broker;

    @XlsxSingleField(columnIndex = 7, label = "Description of Risk")
    private Object riskDescription;

    @XlsxSingleField(columnIndex = 8, label = "Risk State")
    private Object riskState;

    @XlsxSingleField(columnIndex = 9, label = "Product Type")
    private Object productName;

    @XlsxSingleField(columnIndex = 10, label = "ISO Classification Code")
    private Object isoClassificationCode;

    @XlsxSingleField(columnIndex = 11, label = "Deductible or SIR (D/S)")
    private Object retentionType;

    @XlsxSingleField(columnIndex = 12, label = "Deductible Amount / SIR Amount")
    private Object dedOrSirValue;

    @XlsxSingleField(columnIndex = 13, label = "Policy Limit")
    private Object policyLimit;

    @XlsxSingleField(columnIndex = 14, label = "Attachment Limit")
    private Object totalAttachmentLimit;

    @XlsxSingleField(columnIndex = 15, label = "Exposure Basis")
    private Object exposureBasis;

    @XlsxSingleField(columnIndex = 16, label = "Exposure Amount")
    private Object exposureAmount;

    @XlsxSingleField(columnIndex = 17, label = "Rate")
    private Object rateByExposureBasis;

    @XlsxSingleField(columnIndex = 18, label = "Total # of autos")
    private Object totalAutoUnits;

    @XlsxSingleField(columnIndex = 19, label = "Total Written Premium")
    private Object totalWrittenPremium;

    @XlsxSingleField(columnIndex = 20, label = "TRIA Premium")
    private Object triaPremium;

    @XlsxSingleField(columnIndex = 21, label = "Total Premium")
    private Object totalPremium;

    @XlsxSingleField(columnIndex = 22, label = "Fee Amount")
    private Object feeAmount;

    @XlsxSingleField(columnIndex = 23, label = "#' of General Aggregate Reinstatements")
    private Object numberOfGeneralAggregateReinstatements;

    @XlsxSingleField(columnIndex = 24, label = "Commission %")
    private Object commissionPercentage;

    @XlsxSingleField(columnIndex = 25, label = "Commission $")
    private Object commission;

    @XlsxSingleField(columnIndex = 26, label = "Total Technical Premium")
    private Object totalTechnicalPremium;

    @XlsxSingleField(columnIndex = 27, label = "Sold to Technical %")
    private Object soldToTechnicalPercentage;

    @XlsxSingleField(columnIndex = 28, label = "Renewal identifier")
    private Object renewalIdentifier;

    @XlsxSingleField(columnIndex = 29, label = "Treaty")
    private Object treaty;

    @XlsxSingleField(columnIndex = 30, label = "Underwriter")
    private Object underwriter;

    @XlsxSingleField(columnIndex = 31, label = "Premium Due Date")
    private Object premiumDueDate;

    @XlsxSingleField(columnIndex = 32, label = "Carrier")
    private Object carrier;

    @XlsxSingleField(columnIndex = 33, label = "NYFTZ Class")
    private Object nyftzClass;

    @XlsxSingleField(columnIndex = 34, label = "Endorsement #")
    private Object endorsementNumber;

    @XlsxSingleField(columnIndex = 35, label = "Sequence")
    private Object endorsementSequence;

    @XlsxSingleField(columnIndex = 36, label = "Transaction Description")
    private Object transactionDescription;

    @XlsxSingleField(columnIndex = 37, label = "Endorsement Effective Date")
    private Object endorsementEffectiveDate;
}
