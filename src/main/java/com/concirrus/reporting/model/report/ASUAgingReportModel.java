package com.concirrus.reporting.model.report;

import com.concirrus.reporting.annotation.XlsxSheet;
import com.concirrus.reporting.annotation.XlsxSingleField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
@XlsxSheet(value = "ASUAging")
public class ASUAgingReportModel {

    @XlsxSingleField(columnIndex = 0, label = "Policy #")
    private Object policyNumber;

    @XlsxSingleField(columnIndex = 1, label = "Customer Name")
    private Object customerName;

    @XlsxSingleField(columnIndex = 2, label = "Broker Name")
    private Object brokerName;

    @XlsxSingleField(columnIndex = 3, label = "Broker Email")
    private Object brokerEmail;

    @XlsxSingleField(columnIndex = 4, label = "Mailing Address")
    private Object mailingAddress;

    @XlsxSingleField(columnIndex = 5, label = "Underwriter")
    private Object underwriter;

    @XlsxSingleField(columnIndex = 6, label = "Transaction Type")
    private Object transactionType;

    @XlsxSingleField(columnIndex = 7, label = "Invoice Date")
    private Object invoiceDate;

    @XlsxSingleField(columnIndex = 8, label = "Due Date")
    private Object dueDate;

    @XlsxSingleField(columnIndex = 9, label = "Run Date")
    private Object runDate;

    @XlsxSingleField(columnIndex = 10, label = "Invoice Number")
    private Object invoiceNumber;

    @XlsxSingleField(columnIndex = 11, label = "Days Past Due")
    private Object dayPastDue;

    @XlsxSingleField(columnIndex = 12, label = "Gross Due")
    private Object grossDue;

    @XlsxSingleField(columnIndex = 13, label = "Accounting Balance")
    private Object accountingBalance;

    @XlsxSingleField(columnIndex = 14, label = "31 day notice")
    private Object thirtyOneDayNotice;

    @XlsxSingleField(columnIndex = 15, label = "40 day notice")
    private Object fortyDayNotice;

    @XlsxSingleField(columnIndex = 16, label = "54 day notice (ODEN)")
    private Object fiftyFourDayNotice;

    @XlsxSingleField(columnIndex = 17, label = "COMMENTS")
    private Object comments;

    @XlsxSingleField(columnIndex = 18, label = "Additional Interests")
    private Object additionalInterests;
}
