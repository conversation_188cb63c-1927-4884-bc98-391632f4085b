package com.concirrus.reporting.model.report;

import com.concirrus.reporting.annotation.XlsxSheet;
import com.concirrus.reporting.annotation.XlsxSingleField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
@XlsxSheet(value = "Aircraft Information")
public class AircraftInfoReportModel {

    @XlsxSingleField(columnIndex = 0, label = "Registration Number")
    private String registrationNumber;

    @XlsxSingleField(columnIndex = 1, label = "Registration Agency")
    private String registrationAgencyName;

    @XlsxSingleField(columnIndex = 2, label = "Registration Country")
    private String registrationLocationCountry;

    @XlsxSingleField(columnIndex = 3, label = "Serial Number")
    private String serialNumber;

    @XlsxSingleField(columnIndex = 4, label = "Certification Number")
    private String certificationNumber;

    @XlsxSingleField(columnIndex = 5, label = "Insurable Interest Type")
    private String insurableInterestType;

    @XlsxSingleField(columnIndex = 6, label = "Additional Tax/Grain")
    private String insurableInterestTypeAdditionalTaxGrain;

    @XlsxSingleField(columnIndex = 7, label = "Insurable Interest Usage")
    private String insurableInterestUsage;

    @XlsxSingleField(columnIndex = 8, label = "Transit/Storage Condition")
    private String insurableInterestTransitStorageCondition;

    @XlsxSingleField(columnIndex = 9, label = "Aircraft Type")
    private String aircraftType;

    @XlsxSingleField(columnIndex = 10, label = "Weight (kg)")
    private String weightKgs;

    @XlsxSingleField(columnIndex = 11, label = "Build Year")
    private String buildYear;

    @XlsxSingleField(columnIndex = 12, label = "Engines")
    private String engines;

    @XlsxSingleField(columnIndex = 13, label = "Wing Type")
    private String wingType;

    @XlsxSingleField(columnIndex = 14, label = "SEL vs MEL")
    private String selVsMel;

    @XlsxSingleField(columnIndex = 15, label = "Seats (Crew)")
    private String numberOfSeatsCrew;

    @XlsxSingleField(columnIndex = 16, label = "Seats (Passengers)")
    private String numberOfSeatsPassengers;

    @XlsxSingleField(columnIndex = 17, label = "Call Sign")
    private String callSign;

    @XlsxSingleField(columnIndex = 18, label = "Lessor")
    private String lessor;

    @XlsxSingleField(columnIndex = 19, label = "Manufacturer")
    private String manufacturer;

    @XlsxSingleField(columnIndex = 20, label = "Areas of Operation")
    private String areasOfOperation;

    @XlsxSingleField(columnIndex = 21, label = "Base")
    private String base;

    @XlsxSingleField(columnIndex = 22, label = "Purchase Price")
    private String purchasePrice;

    @XlsxSingleField(columnIndex = 23, label = "Purchase Date")
    private String dateOfPurchase;

    @XlsxSingleField(columnIndex = 24, label = "Sum Insured")
    private String sumInsured;

    @XlsxSingleField(columnIndex = 25, label = "Insured From")
    private String insuredFrom;

    @XlsxSingleField(columnIndex = 26, label = "Insured To")
    private String insuredTo;

    @XlsxSingleField(columnIndex = 27, label = "Excess")
    private String excess;

    @XlsxSingleField(columnIndex = 28, label = "Premium")
    private String premium;

    @XlsxSingleField(columnIndex = 29, label = "Hull Value")
    private String hullValue;

    @XlsxSingleField(columnIndex = 30, label = "NIM Deductible")
    private String nimDed;

    @XlsxSingleField(columnIndex = 31, label = "IM Deductible")
    private String imDed;

    @XlsxSingleField(columnIndex = 32, label = "Required Liability Limits")
    private String reqLiabLimits;

    @XlsxSingleField(columnIndex = 33, label = "Required Sub-Limits")
    private String reqSubLimits;

    @XlsxSingleField(columnIndex = 34, label = "Medical Expense Limit")
    private String medicalExpLimit;

    @XlsxSingleField(columnIndex = 35, label = "Annual Flight Hours")
    private String utilAnnFlightHrs;

    @XlsxSingleField(columnIndex = 36, label = "Hull War Coverage")
    private String hullWar;

    @XlsxSingleField(columnIndex = 37, label = "TRIA Hull Coverage")
    private String triaHull;

    @XlsxSingleField(columnIndex = 38, label = "War Liability Coverage")
    private String warLiab;

    @XlsxSingleField(columnIndex = 39, label = "TRIA Liability Coverage")
    private String triaLiab;

    @XlsxSingleField(columnIndex = 40, label = "Excess War")
    private String excessWar;

    @XlsxSingleField(columnIndex = 41, label = "Territory/Area of Operation")
    private String territoryAreaOfOperation;

    @XlsxSingleField(columnIndex = 42, label = "Coverage Type")
    private String coverageType;

    @XlsxSingleField(columnIndex = 43, label = "Home Airport Code")
    private String homeAirportsCode;

    @XlsxSingleField(columnIndex = 44, label = "Home Airport Address")
    private String homeAirportsLocationAddress;

    @XlsxSingleField(columnIndex = 45, label = "Confidence Score")
    private String confidence;
}