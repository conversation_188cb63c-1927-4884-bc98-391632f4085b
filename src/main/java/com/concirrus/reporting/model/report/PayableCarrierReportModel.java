package com.concirrus.reporting.model.report;

import com.concirrus.reporting.annotation.XlsxSheet;
import com.concirrus.reporting.annotation.XlsxSingleField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
@XlsxSheet(value = "Sheet1")
public class PayableCarrierReportModel {

    @XlsxSingleField(columnIndex = 0, label = "Policy Number")
    private Object policyNumber;

    @XlsxSingleField(columnIndex = 1, label = "Name")
    private Object insuredName;

    @XlsxSingleField(columnIndex = 2, label = "Carrier")
    private Object carrier;

    @XlsxSingleField(columnIndex = 3, label = "Invoice Number")
    private Object invoiceNumber;

    @XlsxSingleField(columnIndex = 4, label = "Invoice Date")
    private Object invoiceBilledDate;

    @XlsxSingleField(columnIndex = 5, label = "Invoice Amount")
    private Object invoiceTotalDue;

    @XlsxSingleField(columnIndex = 6, label = "Premium")
    private Object premium;

    @XlsxSingleField(columnIndex = 7, label = "Tax %")
    private Object taxPercentage;

    @XlsxSingleField(columnIndex = 8, label = "Taxes")
    private Object taxAmount;

    @XlsxSingleField(columnIndex = 9, label = "Fees")
    private Object totalFees;

    @XlsxSingleField(columnIndex = 10, label = "Agent %")
    private Object commissionPercentage;

    @XlsxSingleField(columnIndex = 11, label = "Agent Commission")
    private Object commission;

    @XlsxSingleField(columnIndex = 12, label = "MGU %")
    private Object mguPercentage;

    @XlsxSingleField(columnIndex = 13, label = "MGU Commission")
    private Object mguCommission;

    @XlsxSingleField(columnIndex = 14, label = "Payable to Carrier")
    private Object payableCarrier;
}
