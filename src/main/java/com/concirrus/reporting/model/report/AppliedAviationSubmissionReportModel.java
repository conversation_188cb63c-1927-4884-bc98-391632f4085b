package com.concirrus.reporting.model.report;

import com.concirrus.reporting.annotation.XlsxSheet;
import com.concirrus.reporting.annotation.XlsxSingleField;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@NoArgsConstructor
@AllArgsConstructor
@Setter
@XlsxSheet(value = "SUMMARY")
public class AppliedAviationSubmissionReportModel {

    @XlsxSingleField(columnIndex = 0, label = "submission#")
    private Object submissionNumber;

    @XlsxSingleField(columnIndex = 1, label = "received Date")
    private Object receivedDate;

    @XlsxSingleField(columnIndex = 2, label = "Proposed Effective Date")
    private Object proposedEffectiveDate;

    @XlsxSingleField(columnIndex = 3, label = "Submission Status")
    private Object submissionStatus;

    @XlsxSingleField(columnIndex = 4, label = "Underwriter Name")
    private Object underwriterName;

    @XlsxSingleField(columnIndex = 5, label = "Primary Name Insured")
    private Object primaryNameInsured;

    @XlsxSingleField(columnIndex = 6, label = "Has Additional Named Insured")
    private Object hasAdditionalNamedInsured;

    @XlsxSingleField(columnIndex = 7, label = "Aircraft #")
    private Object aircraftNumber;

    @XlsxSingleField(columnIndex = 8, label = "Aircraft Count")
    private Object aircraftCount;

    @XlsxSingleField(columnIndex = 9, label = "Line Of Business")
    private Object lineOfBusiness;

    @XlsxSingleField(columnIndex = 10, label = "Product")
    private Object product;

    @XlsxSingleField(columnIndex = 11, label = "Producer Contact Name")
    private Object producerContactName;

    @XlsxSingleField(columnIndex = 12, label = "Agent Phone")
    private Object agentPhone;

    @XlsxSingleField(columnIndex = 13, label = "Agent Email")
    private Object agentEmail;

    @XlsxSingleField(columnIndex = 14, label = "Agency Name")
    private Object agencyName;

    @XlsxSingleField(columnIndex = 15, label = "Agency Address")
    private Object agencyAddress;

    @XlsxSingleField(columnIndex = 16, label = "Agency City")
    private Object agencyCity;

    @XlsxSingleField(columnIndex = 17, label = "Agency State")
    private Object agencyState;

    @XlsxSingleField(columnIndex = 18, label = "Agency Zip")
    private Object agencyZip;

    @XlsxSingleField(columnIndex = 19, label = "Decline Reason")
    private Object declineReason;
}
