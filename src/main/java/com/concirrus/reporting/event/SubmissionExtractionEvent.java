package com.concirrus.reporting.event;

import com.concirrus.reporting.dto.SubmissionRequestDTO;
import com.concirrus.reporting.entity.ReportRequestInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

@Getter
@Setter
@ToString
public class SubmissionExtractionEvent extends ApplicationEvent {
    private SubmissionRequestDTO submissionRequestDTO;
    private String clientId;
    private ReportRequestInfo reportRequestInfo;

    public SubmissionExtractionEvent(Object source, SubmissionRequestDTO submissionRequestDTO, String clientId, ReportRequestInfo reportRequestInfo) {
        super(source);
        this.submissionRequestDTO = submissionRequestDTO;
        this.clientId = clientId;
        this.reportRequestInfo = reportRequestInfo;
    }
}