package com.concirrus.reporting.event;

import com.concirrus.reporting.entity.ReportRequestInfo;
import com.concirrus.reporting.enums.ReportingType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDate;

@Getter
@Setter
@ToString
public class ReportGenerationEvent extends ApplicationEvent {
    private  LocalDate startDate;
    private LocalDate endDate;
    private ReportingType reportingType;
    private String clientId;
    private ReportRequestInfo reportRequestInfo;

    public ReportGenerationEvent(Object source, LocalDate startDate, LocalDate endDate,ReportingType reportingType, String clientId,  ReportRequestInfo reportRequestInfo) {
        super(source);
        this.startDate = startDate;
        this.endDate = endDate;
        this.reportingType = reportingType;
        this.clientId = clientId;
        this.reportRequestInfo = reportRequestInfo;
    }

}
