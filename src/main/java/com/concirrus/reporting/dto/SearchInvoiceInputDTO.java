package com.concirrus.reporting.dto;

import java.util.List;

public class SearchInvoiceInputDTO {
  private List<String> ids;

  private String policyNumber;

  private String externalQuoteId;

  private String sortInput;

  private String sortOrder;

  private Integer page;

  private Integer size;

  public SearchInvoiceInputDTO() {
  }

  public SearchInvoiceInputDTO(List<String> ids, String policyNumber, String externalQuoteId,
                               String sortInput, String sortOrder, Integer page, Integer size) {
    this.ids = ids;
    this.policyNumber = policyNumber;
    this.externalQuoteId = externalQuoteId;
    this.sortInput = sortInput;
    this.sortOrder = sortOrder;
    this.page = page;
    this.size = size;
  }

  public List<String> getIds() {
    return ids;
  }

  public void setIds(List<String> ids) {
    this.ids = ids;
  }

  public String getPolicyNumber() {
    return policyNumber;
  }

  public void setPolicyNumber(String policyNumber) {
    this.policyNumber = policyNumber;
  }

  public String getExternalQuoteId() {
    return externalQuoteId;
  }

  public void setExternalQuoteId(String externalQuoteId) {
    this.externalQuoteId = externalQuoteId;
  }

  public String getSortInput() {
    return sortInput;
  }

  public void setSortInput(String sortInput) {
    this.sortInput = sortInput;
  }

  public String getSortOrder() {
    return sortOrder;
  }

  public void setSortOrder(String sortOrder) {
    this.sortOrder = sortOrder;
  }

  public Integer getPage() {
    return page;
  }

  public void setPage(Integer page) {
    this.page = page;
  }

  public Integer getSize() {
    return size;
  }

  public void setSize(Integer size) {
    this.size = size;
  }

  @Override
  public String toString() {
    return "SearchInvoiceInputDTO{" + "ids='" + ids + "'," +"policyNumber='" + policyNumber + "'," +"externalQuoteId='" + externalQuoteId + "'," +"sortInput='" + sortInput + "'," +"sortOrder='" + sortOrder + "'," +"page='" + page + "'," +"size='" + size + "'" +"}";
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SearchInvoiceInputDTO that = (SearchInvoiceInputDTO) o;
        return java.util.Objects.equals(ids, that.ids) &&
                            java.util.Objects.equals(policyNumber, that.policyNumber) &&
                            java.util.Objects.equals(externalQuoteId, that.externalQuoteId) &&
                            java.util.Objects.equals(sortInput, that.sortInput) &&
                            java.util.Objects.equals(sortOrder, that.sortOrder) &&
                            java.util.Objects.equals(page, that.page) &&
                            java.util.Objects.equals(size, that.size);
  }

  @Override
  public int hashCode() {
    return java.util.Objects.hash(ids, policyNumber, externalQuoteId, sortInput, sortOrder, page, size);
  }

  public static Builder newBuilder(
      ) {
    return new Builder();
  }

  public static class Builder {
    private List<String> ids;

    private String policyNumber;

    private String externalQuoteId;

    private String sortInput;

    private String sortOrder;

    private Integer page;

    private Integer size;

    public SearchInvoiceInputDTO build() {
                  SearchInvoiceInputDTO result = new SearchInvoiceInputDTO();
                      result.ids = this.ids;
          result.policyNumber = this.policyNumber;
          result.externalQuoteId = this.externalQuoteId;
          result.sortInput = this.sortInput;
          result.sortOrder = this.sortOrder;
          result.page = this.page;
          result.size = this.size;
                      return result;
    }

    public Builder ids(
        List<String> ids) {
      this.ids = ids;
      return this;
    }

    public Builder policyNumber(
        String policyNumber) {
      this.policyNumber = policyNumber;
      return this;
    }

    public Builder externalQuoteId(
        String externalQuoteId) {
      this.externalQuoteId = externalQuoteId;
      return this;
    }

    public Builder sortInput(
        String sortInput) {
      this.sortInput = sortInput;
      return this;
    }

    public Builder sortOrder(
        String sortOrder) {
      this.sortOrder = sortOrder;
      return this;
    }

    public Builder page(
        Integer page) {
      this.page = page;
      return this;
    }

    public Builder size(
        Integer size) {
      this.size = size;
      return this;
    }
  }
}
