package com.concirrus.reporting.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AircraftInformationDTO {
    private String registrationNumber;
    private String registrationAgencyName;
    private String registrationLocationCountry;
    private String serialNumber;
    private String certificationNumber;
    private String insurableInterestType;
    private String insurableInterestTypeAdditionalTaxGrain;
    private String insurableInterestUsage;
    private String insurableInterestTransitStorageCondition;
    private String aircraftType;
    private String weightKgs;
    private String buildYear;
    private String engines;
    private String wingType;
    private String selVsMel;
    private String numberOfSeatsCrew;
    private String numberOfSeatsPassengers;
    private String callSign;
    private String lessor;
    private String manufacturer;
    private String areasOfOperation;
    private String base;
    private String purchasePrice;
    private String dateOfPurchase;
    private String sumInsured;
    private String insuredFrom;
    private String insuredTo;
    private String excess;
    private String premium;
    private String hullValue;
    private String nimDed;
    private String imDed;
    private String reqLiabLimits;
    private String reqSubLimits;
    private String medicalExpLimit;
    private String utilAnnFlightHrs;
    private String hullWar;
    private String triaHull;
    private String warLiab;
    private String triaLiab;
    private String excessWar;
    private String territoryAreaOfOperation;
    private String coverageType;
    private String homeAirportsCode;
    private String homeAirportsLocationAddress;
    private String confidence;
}