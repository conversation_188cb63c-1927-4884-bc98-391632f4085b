package com.concirrus.reporting.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AircraftInformationDTO {
    private String registrationNumber;
    private String registrationAgencyName;
    private String registrationLocationCountry;
    private String serialNumber;
    private String certificationNumber;
    private String insurableInterestType;
    private String insurableInterestTypeAdditionalTaxGrain;
    private String insurableInterestUsage;
    private String insurableInterestTransitStorageCondition;
    private String aircraftType;
    private String weightKgs;
    private String buildYear;
    private String engines;
    private String wingType;
    private String selVsMel;
    private String numberOfSeatsCrew;
    private String numberOfSeatsPassengers;
    private String callSign;
    private String lessor;
    private String manufacturer;
    private String areasOfOperation;
    private String base;
    private String purchasePrice;
    private String dateOfPurchase;
    private String sumInsured;
    private String insuredFrom;
    private String insuredTo;
    private String excess;
    private String premium;
    private String hullValue;
    private String nimDed;
    private String imDed;
    private String reqLiabLimits;
    private String reqSubLimits;
    private String medicalExpLimit;
    private String utilAnnFlightHrs;
    private String hullWar;
    private String triaHull;
    private String warLiab;
    private String triaLiab;
    private String excessWar;
    private String territoryAreaOfOperation;
    private String coverageType;
    private String homeAirportsCode;
    private String homeAirportsLocationAddress;
    private String confidence;

    /**
     * Handles excess field that can be either a String or an array of Strings
     */
    @JsonSetter("excess")
    public void setExcess(JsonNode excessNode) {
        this.excess = processArrayOrStringField(excessNode);
    }

    /**
     * Helper method to process fields that can be either String or Array
     */
    private String processArrayOrStringField(JsonNode node) {
        if (node == null || node.isNull()) {
            return null;
        }
        
        if (node.isArray()) {
            List<String> values = new ArrayList<>();
            for (JsonNode element : node) {
                if (element != null && !element.isNull()) {
                    values.add(element.asText());
                }
            }
            return String.join(", ", values);
        } else {
            return node.asText();
        }
    }
}