package com.concirrus.reporting.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubmissionRequestDTO {
    private String submissionId;
    private String clientId;
    private String jobId;
    private SubmissionBodyDTO submissionBody;
    private String model;
    private String processingTime;
    private String completeProcessingTime;
    private String createdBy;
    private String email;
    private String receivedDate;
    private boolean submissionStatus;
    private String comment;
}
