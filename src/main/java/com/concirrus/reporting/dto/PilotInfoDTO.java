package com.concirrus.reporting.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PilotInfoDTO {
    private String name;
    private String dob;
    private String age;
    private String dateOfCompletion;
    private String trainingFacility;
    private String totalHours;
    private String pilotFlightHours_mm;
    private String pilotFlightHours_se;
    private String pilotFlightHours_me;
    private String pilotFlightHours_fw;
    private String pilotFlightHours_rw;
    private String pilotFlightHours_rg;
    private String pilotFlightHours_tp;
    private String pilotFlightHours_tj;
    private String confidence;
}