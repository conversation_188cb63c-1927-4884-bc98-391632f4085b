package com.concirrus.reporting.dto;

import com.concirrus.reporting.enums.AlertMedium;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class EmailAlertEvent {
    private String clientId;
    private AlertMedium medium;
    private List<String> receivers;
    private String referenceId;
    private String sender;
    private Object content;
    private Instant sentAt;
    private Map<String, String> attachments;
    private String subject;
    private JsonNode templateArgs;
    private String templateId;
    private String lob;
}