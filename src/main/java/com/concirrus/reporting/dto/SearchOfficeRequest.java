package com.concirrus.reporting.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchOfficeRequest {
    private Collection<String> officeIds;
    private String officeName;
    private String brokerageId;
    private String brokerageName;
    private String addressCity;
    private String addressZipCode;
}

