package com.concirrus.reporting.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BodyDTO {
    private String LOB;
    private List<String> feature_catalog_list;
    private List<PilotInfoDTO> pilotInfo;
    private List<AircraftInformationDTO> aircraftInformation;
    private RiskDetailsDTO riskDetails;
    private List<AdditionalInsuredInfoDTO> additionalInsuredInfo;
    private Map<String, Object> confidenceLevel;
}