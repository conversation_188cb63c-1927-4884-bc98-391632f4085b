package com.concirrus.reporting.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BodyDTO {
    private String LOB;
    private List<String> feature_catalog_list;
    private List<PilotInfoDTO> pilotInfo;
    private List<AircraftInformationDTO> aircraftInformation;
    private RiskDetailsDTO riskDetails;
    private List<AdditionalInsuredInfoDTO> additionalInsuredInfo;
    private Map<String, Object> confidenceLevel;

    /**
     * Handles confidenceLevel field where values can be either single values or arrays
     */
    @JsonSetter("confidenceLevel")
    public void setConfidenceLevel(JsonNode confidenceLevelNode) {
        if (confidenceLevelNode == null || confidenceLevelNode.isNull()) {
            this.confidenceLevel = null;
            return;
        }

        this.confidenceLevel = new HashMap<>();
        confidenceLevelNode.fields().forEachRemaining(entry -> {
            String key = entry.getKey();
            JsonNode value = entry.getValue();

            if (value.isArray()) {
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < value.size(); i++) {
                    if (i > 0) sb.append(", ");
                    sb.append(value.get(i).asText());
                }
                this.confidenceLevel.put(key, sb.toString());
            } else {
                this.confidenceLevel.put(key, value.asText());
            }
        });
    }
}