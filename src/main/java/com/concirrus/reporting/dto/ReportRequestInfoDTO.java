package com.concirrus.reporting.dto;

import com.concirrus.reporting.entity.ReportRequestInfo;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class ReportRequestInfoDTO {
    private String id;
    private String clientId;
    private String status;
    private String fileDownloadLink;
    private String fileName;
    private String reportType;
    private LocalDate startDate;
    private LocalDate endDate;
    private String errorMessage;
    private LocalDate runDate;
    private String createdBy;
    private String createdByUserName;
    private LocalDateTime createdAt;


    public static ReportRequestInfoDTO toDTO(ReportRequestInfo reportRequestInfo) {
        ReportRequestInfoDTO reportRequestInfoDTO = new ReportRequestInfoDTO();

        reportRequestInfoDTO.setId(reportRequestInfo.getId());
        reportRequestInfoDTO.setClientId(reportRequestInfo.getClientId());
        reportRequestInfoDTO.setStatus(reportRequestInfo.getStatus());
        reportRequestInfoDTO.setFileDownloadLink(reportRequestInfo.getFileDownloadLink());
        reportRequestInfoDTO.setFileName(reportRequestInfo.getFileName());
        reportRequestInfoDTO.setReportType(reportRequestInfo.getReportType());
        reportRequestInfoDTO.setStartDate(reportRequestInfo.getStartDate());
        reportRequestInfoDTO.setEndDate(reportRequestInfo.getEndDate());
        reportRequestInfoDTO.setErrorMessage(reportRequestInfo.getErrorMessage());
        reportRequestInfoDTO.setRunDate(reportRequestInfo.getRunDate());
        reportRequestInfoDTO.setCreatedAt(reportRequestInfo.getCreatedAt());
        reportRequestInfoDTO.setCreatedBy(reportRequestInfo.getCreatedBy());
        reportRequestInfoDTO.setCreatedByUserName(reportRequestInfo.getCreatedByUserName());

        return reportRequestInfoDTO;
    }
}
