package com.concirrus.reporting.dto;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RiskDetailsDTO {
    private String umr;
    private String type;
    private String insured;
    private String insuredNumberAndStreet;
    private String insuredAdditionalAddressLine;
    private String insuredCityName;
    private String insuredPostalCode;
    private String insuredCountry;
    private String inceptionDate;
    private String expiryDate;
    private String interest;
    private String limitOfLiability;
    private String reinsuredsRetention;
    private String situation;
    private String choiceOfLaw;
    private String jurisdictionLocationCountry;
    private String premium;
    private String instalmentAmount;
    private String instalmentPercentage;
    private String instalmentDueDate;
    private String orderHereon;
    private String basisOfWrittenLines;
    private String slipLeader;
    private String bureauLeader;
    private String basisOfClaimsAgreement;
    private String settlementDueDate;
    private String taxPayableByInsurers;
    private String countryOfOrigin;
    private String correspondentBrokerName;
    private String otherBrokerAdditionalAddressLine;
    private String otherBrokerAddressCityName;
    private String otherBrokerAddressPostalCode;
    private String otherBrokerAddressCountry;
    private String riskCode;
    private String riskCodePercentage;
    private String regulatoryClientClassification;
    private String feePayableByClient;
    private String totalBrokerage;
    private String otherDeductionsFromPremium;
    private String coverageLimit;
    private String excess;

    /**
     * Handles riskCode field that can be either a String or an array of Strings
     */
    @JsonSetter("riskCode")
    public void setRiskCode(JsonNode riskCodeNode) {
        this.riskCode = processArrayOrStringField(riskCodeNode);
    }

    /**
     * Handles coverageLimit field that can be either a String or an array of Strings
     */
    @JsonSetter("coverageLimit")
    public void setCoverageLimit(JsonNode coverageLimitNode) {
        this.coverageLimit = processArrayOrStringField(coverageLimitNode);
    }

    /**
     * Handles excess field that can be either a String or an array of Strings
     */
    @JsonSetter("excess")
    public void setExcess(JsonNode excessNode) {
        this.excess = processArrayOrStringField(excessNode);
    }

    /**
     * Helper method to process fields that can be either String or Array
     */
    private String processArrayOrStringField(JsonNode node) {
        if (node == null || node.isNull()) {
            return null;
        }
        
        if (node.isArray()) {
            List<String> values = new ArrayList<>();
            for (JsonNode element : node) {
                if (element != null && !element.isNull()) {
                    values.add(element.asText());
                }
            }
            return String.join(", ", values);
        } else {
            return node.asText();
        }
    }
}