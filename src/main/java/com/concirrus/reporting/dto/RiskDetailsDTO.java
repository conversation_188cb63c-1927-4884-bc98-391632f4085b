package com.concirrus.reporting.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RiskDetailsDTO {
    private String umr;
    private String type;
    private String insured;
    private String insuredNumberAndStreet;
    private String insuredAdditionalAddressLine;
    private String insuredCityName;
    private String insuredPostalCode;
    private String insuredCountry;
    private String inceptionDate;
    private String expiryDate;
    private String interest;
    private String limitOfLiability;
    private String reinsuredsRetention;
    private String situation;
    private String choiceOfLaw;
    private String jurisdictionLocationCountry;
    private String premium;
    private String instalmentAmount;
    private String instalmentPercentage;
    private String instalmentDueDate;
    private String orderHereon;
    private String basisOfWrittenLines;
    private String slipLeader;
    private String bureauLeader;
    private String basisOfClaimsAgreement;
    private String settlementDueDate;
    private String taxPayableByInsurers;
    private String countryOfOrigin;
    private String correspondentBrokerName;
    private String otherBrokerAdditionalAddressLine;
    private String otherBrokerAddressCityName;
    private String otherBrokerAddressPostalCode;
    private String otherBrokerAddressCountry;
    private String riskCode;
    private String riskCodePercentage;
    private String regulatoryClientClassification;
    private String feePayableByClient;
    private String totalBrokerage;
    private String otherDeductionsFromPremium;
}