package com.concirrus.reporting.dto;

import com.concirrus.reporting.enums.ReportingType;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.util.Map;

import static com.concirrus.reporting.constant.RequestConstant.CANNOT_BE_NULL;

@Data
public class ReportRequestDTO {
    private LocalDate startDate;
    private LocalDate endDate;

    @NotNull(message = CANNOT_BE_NULL)
    private ReportingType reportingType;

}
