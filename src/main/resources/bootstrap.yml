spring:
  cloud:
    kubernetes:
      secrets:
        enableApi: true
        enabled: true
        sources:
          - name: mongo-secret
          - name: reporting-aws-secret
      reload:
        enabled: true
        monitoring-config-maps: false
        monitoring-secrets: false
        mode: event
        #Below property will work only when monitoring enabled will be true.
        strategy: restart_context
      config:
        enabled: true
        sources:
          - name: mongo-config
          - name: cloud-provider-config
          - name: reporting-config
          - name: slack-config