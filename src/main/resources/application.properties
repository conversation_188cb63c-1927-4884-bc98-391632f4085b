#concirrus credentials

server.port=8080
server.servlet.context-path=/core/reporting-service

# S3
cloud.provider=gcp
cloud.gcp.project-id=test
cloud.aws.accessKey=${AWS_ACCESS_KEY}
cloud.aws.secretKey=${AWS_SECRET}
cloud.aws.region=${AWS_REGION}
last-days-report=${LAST_DAYS_REPORT}
cloud.bucket.name.aws=${AWS_REPORTING_BUCKET}
cloud.bucket.name.gcp=${GCP_REPORTING_BUCKET}
report.link.expiration-days=${REPORT_LINK_EXPIRATION_IN_DAYS}

mongo.reporting.db=${MONGO_REPORTING_DB}
mongo.policy.db=${MONGO_POLICY_DB}
mongo.audit.db=${MONGO_AUDIT_DB}
mongo.policy.uri=mongodb://${MONGO_USERNAME:username}:${MONGO_PASSWORD:password}@${MONGO_IPS:************:27017}/${MONGO_DEFAULT_DB:defaultauthdb}?authSource=admin

#ACTUATOR
management.endpoints.web.base-path=/manage
management.endpoints.web.exposure.include=health, info, prometheus, restart
management.endpoint.info.enabled=true
management.endpoint.health.enabled=true
management.endpoint.health.show-details=always
management.endpoint.restart.enabled=true


# GCP Pub/Sub Configuration
 cloud.queue.in.consumer-reporting=${REPORTING_SERVICE_SUB}
 cloud.queue.out.submission=${SUBMISSION_QUEUE_PUBLISHER}
 queue.url.out.slack-alert=${SQS_SLACK_ALERT_CHANNEL}

# external service API
external.endpoints.invoice-data-service.baseUrl=http://invoice-management-service/core/invoice-service/invoice/search
external.endpoints.brokerage-hub-service.baseUrl=http://brokerage-hub-service/core/brokerage-service
external.endpoints.audit-service.baseUrl=http://audit-service/core/audit-service
external.endpoints.authorisation-service.baseUrl=http://authorisation-service/core/authorisation-service
external.endpoints.access-management-service.baseUrl=${ACCESS_MANAGEMENT_SERVICE_BASE_URL}